# Wenprise Pinyin Slug #
Contributors: iwillhappy1314
Donate link: https://www.wpzhiku.com/
Tags: slug, Pinyin Attachment Name,Pinyin Slugs,Pinyin SEO,Pinyin Permalinks
Requires at least: 3.4
Requires PHP: 7.1.0
Tested up to: 6.6
Stable tag: 3.1.1
Domain Path: languages
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

自动转换 WordPress 中的中文文章别名、分类项目别名、图片文件名称为汉语拼音或英文翻译。

## Description ##

自动转换 WordPress 中的中文文章别名、分类项目别名、图片文件名称为汉语拼音或英文翻译。

### 插件的主要功能 ###

* 转换文章别名为拼音或英文翻译，支持快速编辑时转换
* 转换分类目录、标签或自定义分类法别名为拼音或英文翻译，支持快速编辑时转换
* 当别名为英文，或手动设置了中文别名时，保持原样
* 可选的转换中文图片名为拼音或英文翻译
* 支持自定义转换方式为全拼或第一个字母
* 支持设置拼音之间的间隔字符
* 支持截取转换后的拼音或英文翻译为设置的长度
* 支持古腾堡编辑器
* 如果出现中英文混合的情况、保留英文、只转换中文部分

### 为什么需要这个插件？###

当 URL 中出现中文时，中文部分会被自动转码，不知道的人看起来就是一团乱码，缺乏可读性。虽然有些浏览器可以在地址栏中显示中文，复制分享给别人的时候，URL 中文部分显示的也是被转码后的代码。

不同的操作系统环境，FTP 传输工具使用的文件名编码不同，迁移服务器时，如果文件名编码发生了变换，中文文件名很可能会变成乱码，导致文件不能访问。

### 什么情况下使用这个插件？ ###

如果你的站点是给客户用的，建议使用这个插件，因为他们很可能不会意识到在 URL 和文件名中使用中文的问题。此插件可以自动帮他们处理。

如果站点是你自己用的，并且你是一个细心的人，发布内容时，会手动编辑别名和文件名，这个插件对你来说就是多余的。不需要安装。


## Installation ##

1. 上传插件到`/wp-content/plugins/` 目录，或在 WordPress 安装插件界面搜索 "Wenprise Pinyin Slug"，点击安装。
2. 在插件管理菜单激活插件

## Frequently Asked Questions ##

### 插件支持繁体中文吗？ ###

经测试，插件是支持繁体中文的。


## Screenshots ##

1. 文章别名自动转换
2. 分类别名自动转换
3. 文件名自动转换

## Changelog ##
### 3.1.1 ###
* 解决分类名称为英文时，不调用 sanitize_title 的问题

### 3.1.0 ###
* Bugfix, 解决新版古滕堡编辑器保存文章时多次进行拼音转换的问题
* PHP 版本要求提高到 7.4

### 3.0.0 ###
* PHP 版本要求提高到 7.1.0
* 移除没有使用的依赖库

### 1.5.4 ###
* update test up

### 1.5.3 ###
* 增加对 wp_insert_post 函数的支持

### 1.5.2 ###
* 增加 wenprise_converted_slug Filter，允许第三方代码修改转换后的别名

### 1.5.1 ###
* 修复翻译模式下，分隔符无效的问题

### 1.5.0 ###
* 修改后台设置，合并翻译设置和拼音转换模式设置

### 1.4.13 ###
* Bugfix
* 中英混合时，转换英文为小写

### 1.4.10 ###
* 使用严格模式进行设置测试

### 1.4.8 ###
* 修复没有后缀时，导致 sanitize_file_name 函数多一个 . 的问题

### 1.4.5 ###
* 更改访问设置的权限为管理员

### 1.4.4 ###
* 添加禁用文件名自动转换的选项

### 1.4.3 ###
* 设置拼音默认长度为 60 个字符

### 1.4.1 ###
* 修复古腾堡编辑器在无草稿直接发布时不能转换别名的 Bug

### 1.4.0 ###
* 添加百度翻译 API，如果选择不使用或者翻译失败，则使用拼音转换的方式生成别名，则使用拼音转换的方式生成别名

### 1.3.1 ###
* 插件列表添加设置链接

### 1.3.0 ###
* 支持古腾堡编辑器

### 1.2.2 ###
* 长度限制默认设置为空，为空时，不截取转换后的别名

### 1.2.1 ###
* Bugfix

### 1.2 ###
* 增加转换别名字符长度限制

### 1.1.3 ###
* 添加 PHP 版本检查

### 1.1.2 ###
* 解决更新分类时，别名不能转拼音的 Bug

### 1.1 ###
* 增加设置项，可以设置是否启用分隔符、分隔符样式以及转换方式，全拼或者第一个字母

= 1.0 =
* 第一个版本，支持文章别名、分类项目别名、和图片文件名的自动转换。
