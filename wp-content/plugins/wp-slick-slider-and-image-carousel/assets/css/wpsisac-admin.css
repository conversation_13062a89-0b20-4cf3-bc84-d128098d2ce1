/* Shortcode Preview */
.wpsisac-builder-shrt-prev{background-color: #e7e7e7; padding: 10px; border: 1px solid #ccc; font-size: 15px;text-align: center;}
.wpsisac-builder-shrt-title span{border-bottom: 1px solid #999; display: inline-block; padding: 0 0 5px 0;}

.risk-free-guarantee{display:block; margin-top:10px;}
.risk-free-guarantee span{font-weight:bold; color:#FF1000;}
.risk-free-guarantee span.heading{ color:#6c63ff;}

.wpsisac-pro-tag {padding: 4px 7px 4px 5px;background-color:#cdd9f3;color: #1e2b48; position: relative; font-size: 10px; margin: 0 10px; display: inline-block; line-height: 1.1;}
.wpsisac-pro-tag::before { content: ""; left: -10px; top: 0; border-top: 10px solid transparent;border-right: 10px solid #cdd9f3;border-bottom: 10px solid transparent; position: absolute;}
.wpsisac-pro-feature { opacity: 0.5;}
.wpsisac-pro-feature:hover{opacity: 0.8;}
.pro-notice{background-color: #fcecce; border: 1px dashed #f8c057; padding: 3px 10px; font-size:15px;}
#wpsisac-post-metabox-pro tr.wpsisac-pro-feature{border-bottom:1px solid #f1f1f1;}
#wpsisac-post-metabox-pro tr.wpsisac-pro-feature th,
#wpsisac-post-metabox-pro tr.wpsisac-pro-feature td{ vertical-align:top; padding:10px; }
#wpsisac-post-metabox-pro tr.wpsisac-pro-feature th:first-child,
#wpsisac-post-metabox-pro tr.wpsisac-pro-feature td:first-child{width:40%; }
.wpsisac-slide-img{border: 1px solid #ccc; padding: 2px;}

/****** Start - Solutions & Features Page CSS *****/
.wpsisac-sf-center{text-align:center;}
.slick_slider_page_wpsisac-solutions-features #wpwrap { background: #f7faff;}
.wpsisac-sf-wrap{max-width: 860px;margin: 0 auto;}
.wpsisac-sf-heading{font-size: 28px;font-weight: 700;letter-spacing: -1px;text-align: center;margin-top: 30px; margin-bottom:5px;}
.wpsisac-sf-image{margin:25px 0 15px 0;}
.wpsisac-sf-cont {color: #000; font-size:15px;}
.wpsisac-sf-top-rsn p, .wpsisac-sf-sf-testimonial-wrap p{color: #000; font-size:15px;}
.wpsisac-sf-left{text-align:left;}
.wpsisac-sf-feature__text h3{font-size: 22px;font-weight: 700;margin:0;}
.wpsisac-sf-solutions-box-grid	{display: -webkit-box;display: -ms-flexbox;display: flex;-webkit-box-orient: vertical;-webkit-box-direction: normal;-ms-flex-direction: column; flex-direction: column;-webkit-box-pack: center;-ms-flex-pack: center;justify-content: center;}
.wpsisac-sf-team{margin-bottom:50px; border-radius: 10px;border: 1px solid #e5ecf6; padding:10px 30px 30px 30px;}
.wpsisac-sf-blue{color:#6c63ff; font-weight:bold;}
.slick_slider_page_wpsisac-solutions-features #wpcontent{padding: 0 0 40px; line-height: normal;}
.wpsisac-sf-wrap .wpsisac-sf-inr{padding: 30px 40px 0;}
.wpsisac-sf-wrap .wpsisac-sf-welcome-wrap{ margin-bottom:50px; position: relative;  -webkit-box-sizing: border-box;  box-sizing: border-box;  background: #fff;  padding: 30px;  border-radius: 10px;  border: 1px solid #e5ecf6; }
.wpsisac-sf-welcome-wrap{margin-top:30px;}

.wpsisac-sf-subtitle{color: #6c7781; font-size: 16px; font-weight: 600; margin-bottom: 6px;}
.wpsisac-sf-title{font-size: 32px; font-weight: 600; color: #23282d; margin-top: 0; margin-bottom: 20px;}
.wpsisac-sf-ttl{font-size: 23px; font-weight: 600; color: #23282d; margin-top: 0; margin-bottom: 20px;}
.wpsisac-sf-content{margin: 0 0 20px; font-size: 22px;  color: #646970;}
.wpsisac-sf-btn{display: inline-block; font-size: 18px; padding: 10px 25px; border-radius: 100px;  background-color: #ff5d52; border-color: #ff5d52; color: #fff !important; font-weight: 600; text-decoration: none;}
.wpsisac-sf-btn-orange{ background-color: #FF1000; border-color: #FF1000 ;}
.wpsisac-sf-btn:hover,
.wpsisac-sf-btn:focus{background-color: #ff5d52; border-color: #ff5d52;}
.wpsisac-sf-btn-orange:hover,
.wpsisac-sf-btn-orange:focus {background-color: #D01003 ; border-color: #D01003 ;}
.wpsisac-sf-fp-ttl{font-size: 23px; line-height: 1.4em; margin-bottom: 10px; font-weight: 600; text-align: center;}
.wpsisac-sf-fp-box-wrp{display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; gap: 10px;}
.wpsisac-sf-fp-box-wrp .wpsisac-sf-fp-box{position: relative; border: 1px solid #c3e6cb; padding: 10px; text-align: center;}
.wpsisac-sf-fp-box-wrp .wpsisac-sf-pro-box{border: 1px solid #f5c6cb;}
.wpsisac-sf-fp-box-wrp>*{flex: 0 0 26.8%;}
.wpsisac-sf-fp-box-wrp i.dashicons{font-size: 25px; display: inline-block; color: #528bfa; width: 30px; height: 30px; line-height: 30px;}
.wpsisac-sf-fp-box-wrp .wpsisac-sf-box-ttl{font-size: 16px;}
.wpsisac-sf-tag{position: absolute; display: inline-block; top: 0; right: 0; background-color: #d4edda; color: #155724; padding: 1px 8px; text-transform: uppercase; font-size: 12px; }
.wpsisac-sf-pro-box .wpsisac-sf-tag{background-color: #f8d7da; color: #721c24;}

.wpsisac-rc-wrap{max-width: 550px; margin: 15px auto 0 auto; text-align: left;}
.wpsisac-rc-wrap .wpsisac-rc-inr{display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin-bottom: 15px;}
.wpsisac-rc-wrap .wpsisac-rc-icon{margin-right: 15px; width: 60px; height: 60px;}
.wpsisac-rc-wrap .wpsisac-rc-icon img{width: 100%; height: 100%;}
.wpsisac-rc-wrap .wpsisac-rc-inr h3{font-size: 16px; margin: 0 0 5px 0;}
.wpsisac-rc-wrap .wpsisac-rc-inr p{margin: 0;}
.wpsisac-rc-wrap .wpsisac-rc-bg-box{ background-color:#f1f1f1; border: 1px solid #d1d1d1; padding: 5px 10px; }

.wpsisac-solutions-section{margin-bottom: 20px;}
.wpsisac-solutions-box-wrap{display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; gap: 15px; margin-top:50px; margin-bottom:10px;}
.wpsisac-solutions-box-wrap>*{flex: 1 0 21%;}

.wpsisac-solutions-box-wrap .wpsisac-box-ttl{background-color: #cce5ff; color: #004085; font-size: 16px; padding: 10px;}
.wpsisac-solutions-box-wrap ul{padding: 0 10px; margin: 0;}
.wpsisac-solutions-box-wrap ul li{position: relative; font-size: 14px; border-bottom: 1px solid #e7e7e7; margin: 0; padding: 8px 0;}
.wpsisac-solutions-box-wrap ul li:last-child{border-bottom: none;}

.wpsisac-sf-welcome-wrap{margin-top:30px;}
.wpsisac-sf-features-ttl{margin-top:45px; margin-bottom: 15px;}
.wpsisac-sf-features-ttl h2{margin-bottom: 5px; color: #155724;}
.wpsisac-features-section .wpsisac-features-section-inr{padding: 15px 30px; margin-bottom:30px; text-align:center;}
.wpsisac-features-section ul.wpsisac-features-box-grid{margin:0px;display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; gap: 15px; list-style: none !important; margin-bottom:15px;}
ul.wpsisac-features-box-grid>*{flex: 1 0 30%; text-align:center; font-size:13px; font-weight:bold;}
ul.wpsisac-features-box-grid li{border:1px solid #f1f1f1; padding:10px 0;}
ul.wpsisac-features-box-grid li img{width:100%;}

.bg-highlight{background:#bde1f9;color:#000;padding: 0 5px; display:inline-block;}
.wpsisac-testimonial-section-inr{text-align:center;}
.wpsisac-testimonial-box-wrap{margin-bottom:15px;}
.wpsisac-testimonial-box-wrap{display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; gap: 15px;}
.wpsisac-testimonial-box-wrap>*{flex: 1 0 21%;}
.wpsisac-testimonial-box-wrap .wpsisac-testimonial-box-grid{border: 1px solid #ddd;background:#fff;  box-shadow: 0 3px 2px rgba(0,0,0,.05); padding: 30px;text-align: center;display: flex;flex-direction: column;align-items: center;}
.wpsisac-testimonial-box-wrap h3{margin-top:0px;}
.wpsisac-testimonial-clnt{margin:15px 0 0 0; font-size:16px; font-weight:bold;}
/***** End - Solutions & Features Page CSS *****/

/* welcome-screen-css start -M */
.wpsisac-inner-Bonus-class{background: #46b450;
  border-radius: 20px;
  font-weight: 700;
  padding: 5px 10px;
  color: #fff;
    line-height: 1;
  font-size: 12px;}

.wpsisac-black-friday-feature{padding: 30px 40px;
  background: #fafafa;
  border-radius: 20px 20px 0 0;
  gap: 60px;
  align-items: center;
  flex-direction: row;
  display: flex;}
.wpsisac-black-friday-feature .wpsisac-inner-deal-class{flex-direction: column;
  gap: 15px;
  display: flex;
  align-items: flex-start;}
.wpsisac-black-friday-feature ul li{text-align: left;}
.wpsisac-black-friday-feature .wpsisac-inner-list-class {
  display: grid;
  grid-template-columns: repeat(4,1fr);
  gap: 10px;
}
.wpsisac-black-friday-feature .wpsisac-list-img-class {
  min-height: 95px;
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 20px;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  padding: 10px;color: #000;
  font-size: 12px;
}
.wpsisac-black-friday-banner-wrp .wpsisac-list-img-class img {
  width: 100%;
  flex: 0 0 40px;
  font-size: 20px;
  height: 40px;
  width: 40px;
  box-shadow: inset 0px 0px 15px 2px #c4f2ac;
  border-radius: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.wpsisac-main-feature-item{background: #fafafa;
  padding: 20px 15px 40px;
  border-radius: 0 0 20px 20px;margin-bottom: 40px;}
.wpsisac-inner-feature-item{display: flex;
  gap: 30px;
  padding: 0 15px;}
.wpsisac-list-feature-item {
  border: 1px solid #ddd;
  padding: 10px 15px;
  border-radius: 8px;text-align: left;
}
.wpsisac-list-feature-item img {
  width: 36px !important;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 50%;margin-bottom: 5px;
}
.wpsisac-list-feature-item h5{margin: 0;
  font-weight: bold;font-size: 16px;
  text-decoration: underline;
  text-underline-position: under;
  color: #000;}
.wpsisac-list-feature-item p {
  color: #505050;
  font-size: 12px;
  margin-bottom: 0;
}

/* welcome-screen-css end -M */


/***** Vertical Tabs *****/
.wpsisac-vtab-wrap, .wpsisac-vtab-wrap *{-webkit-box-sizing: border-box; -moz-box-sizing: border-box;box-sizing: border-box; outline:none;}
.wpsisac-vtab-wrap{background-color: #f5f5f5; border-top:1px solid #e2dddb;}
.wpsisac-vtab-wrap .form-table{clear: none; margin: 0;}
.wpsisac-vtab-wrap .wpsisac-tab-info-wrap{border-bottom: 1px solid #e2dddb; padding: 0 0 5px 0;}
.wpsisac-vtab-wrap .wpsisac-tab-title{font-size: 14px; font-weight: 600;}
.wpsisac-vtab-wrap .wpsisac-tab-desc{font-size: 13px; font-style: italic; margin: 5px 0 0 0; display: block;}
.wpsisac-vtab-nav-wrap{float: left; list-style: outside none none; padding: 0 !important; position: relative; width: 180px; word-wrap: break-word; margin: 0 !important; background-color: #f5f5f5;}
.wpsisac-vtab-nav-wrap .wpsisac-vtab-nav{display: block; margin: 0; padding: 0; position: relative; width: 100%;}
.wpsisac-vtab-nav-wrap .wpsisac-vtab-nav a {color: #222; display: block; padding: 12px 10px; width: 100%; text-decoration: none; box-shadow: none; border-width: 1px; border-color: #e2dddb; border-style: none solid solid none; -webkit-transition: box-shadow 0.2s ease-in-out; transition: box-shadow 0.2s ease-in-out;}
.wpsisac-vtab-nav-wrap .wpsisac-vtab-nav a:hover{box-shadow:3px 0 0 0 #222 inset;}
.wpsisac-vtab-nav.wpsisac-active-vtab a{border-style: none none solid none; background-color:#fff; box-shadow:3px 0 0 0 #222 inset;}
.wpsisac-vtab-cnt-wrp{background: #fff none repeat scroll 0 0; margin-left: 179px; padding: 10px 20px 10px 20px; min-height: 225px; border-left: 1px solid #e2dddb;}
.wpsisac-vtab-cnt{display: none;}