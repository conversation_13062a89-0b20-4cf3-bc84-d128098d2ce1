<?php
/**
 * Template for Carousel - Design 3
 *
 * @package WP Slick Slider and Image Carousel
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}
?>
<div class="wpsisac-image-slide">
	<div class="wpsisac-slide-wrap" style="<?php echo esc_attr( $slider_height_css ); ?>">	
		<img <?php if( $lazyload ) { ?>data-lazy="<?php echo esc_url( $slider_orig_img ); ?>"<?php } ?> src="<?php echo esc_url( $slider_img ); ?>" alt="<?php the_title_attribute(); ?>" />
		<div class="wpsisac-slider-content">
			<div class="wpsisac-bg-overlay wp-medium-7 wpcolumns">
				<h2 class="wpsisac-slide-title"><?php the_title(); ?></h2>
				<?php if( $show_content ) { ?>
					<div class="wpsisac-slider-short-content"><?php the_content(); ?></div>
				<?php }

				if( $sliderurl != '' ) { ?>
					<div class="wpsisac-readmore"><a href="<?php echo esc_url( $sliderurl ); ?>" class="wpsisac-slider-readmore"><?php esc_html_e( 'Read More', 'wp-slick-slider-and-image-carousel' ); ?></a></div>
				<?php } ?>
			</div>
		</div>
	</div>
</div>