# Copyright (C) 2024 WP OnlineSupport, Essential Plugin
# This file is distributed under the same license as the WP Slick Slider and Image Carousel plugin.
msgid ""
msgstr ""
"Project-Id-Version: WP Slick Slider and Image Carousel 3.7\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wp-slick-slider-and-image-carousel\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-06-07T14:48:31+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: wp-slick-slider-and-image-carousel\n"

#. Plugin Name of the plugin
#: C:\laragon\www\testingserver\wp-content\plugins\wp-slick-slider-and-image-carousel\wp-slick-image-slider.php
#: includes/admin/wpsisac-how-it-work.php:170
msgid "WP Slick Slider and Image Carousel"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: C:\laragon\www\testingserver\wp-content\plugins\wp-slick-slider-and-image-carousel\wp-slick-image-slider.php
msgid "https://www.essentialplugin.com/wordpress-plugins/wp-slick-slider-and-image-carousel/"
msgstr ""

#. Description of the plugin
#: C:\laragon\www\testingserver\wp-content\plugins\wp-slick-slider-and-image-carousel\wp-slick-image-slider.php
msgid "Easy to add and display wp slick image slider and carousel. Also added Gutenberg block support."
msgstr ""

#. Author of the plugin
#: C:\laragon\www\testingserver\wp-content\plugins\wp-slick-slider-and-image-carousel\wp-slick-image-slider.php
msgid "WP OnlineSupport, Essential Plugin"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:48
msgid "How it works, our plugins and offers"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:48
#: includes/admin/wpsisac-how-it-work.php:25
msgid "How It Works"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:51
msgid "Overview - WP Slick Slider and Image Carousel"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:51
msgid "Overview"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:54
msgid "Upgrade To PRO - WP Slick Slider and Image Carousel"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:54
msgid "Upgrade To PRO"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:122
msgid "WP Slick Slider and Image Carousel - Settings"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:125
msgid "More Premium - Settings"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:174
msgid "ID:"
msgstr ""

#: includes/admin/class-wpsisac-admin.php:184
msgid "Image"
msgstr ""

#: includes/admin/metabox/wpsisac-post-metabox.php:23
msgid "Read More Link"
msgstr ""

#: includes/admin/metabox/wpsisac-post-metabox.php:27
msgid "Enter read more link. eg. "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:14
msgid "Utilize this <a href=\"%s\" target=\"_blank\">Premium Features (With Risk-Free 30 days money back guarantee)</a> to get best of this plugin with Annual or Lifetime bundle deal."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:21
msgid "Layouts "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:21
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:29
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:37
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:45
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:53
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:61
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:69
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:77
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:85
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:93
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:101
msgid "PRO"
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:24
msgid "3 ( Slider, Carousel, Variable width ). In lite version only Slider and Carousel layout."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:29
msgid "Designs "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:32
msgid " In lite version only 5+ design."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:37
msgid "Arrows design "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:40
#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:48
msgid " In lite version only one design."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:45
msgid "Dots Design "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:53
msgid "Navigation Support "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:56
msgid "Thumbnail navigation support to some designs."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:61
msgid "Custom Read More link Text "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:64
msgid "Add a custom name for read more link"
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:69
msgid "WP Templating Features "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:72
#: includes/admin/settings/solution-features/basicpro-tab.php:68
msgid "You can modify plugin html/designs in your current theme."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:77
msgid "Shortcode Generator "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:80
msgid "Play with all shortcode parameters with preview panel. No documentation required."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:85
msgid "Drag & Drop Slide Order Change "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:88
msgid "Arrange your desired slides with your desired order and display."
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:93
msgid "Page Builder Support "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:96
msgid "Gutenberg Block, Elementor, Bevear Builder, SiteOrigin, Divi, Visual Composer and Fusion Page Builder Support"
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:101
msgid "Exclude Slider Post and Exclude Some Categories "
msgstr ""

#: includes/admin/metabox/wpsisac-post-setting-metabox-pro.php:104
msgid "Do not display the slider post & Do not display the slider category for particular categories."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:39
msgid "Free"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:42
msgid "Premium"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:48
msgid "Designs"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:48
msgid "Designs that make your website better"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:53
msgid "Shortcodes"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:53
msgid "Shortcode provide output to the front-end side"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:54
msgid "2 (Slider, Carousel)"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:55
msgid "3 (Slider, Carousel, Variable width )"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:58
msgid "Shortcode Parameters"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:58
msgid "Add extra power to the shortcode"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:63
msgid "Shortcode Generator"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:63
msgid "Play with all shortcode parameters with preview panel. No documentation required!!"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:68
#: includes/admin/wpsisac-how-it-work.php:163
msgid "WP Templating Features"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:73
msgid "Drag &amp; Drop Slide Order Change"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:73
msgid "Arrange your desired slides with your desired order and display"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:78
msgid "Navigation Support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:78
msgid "Thumbnail navigation support to some designs"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:83
msgid "Loop Control"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:83
msgid "Infinite scroll control"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:88
#: includes/admin/wpsisac-how-it-work.php:158
msgid "Gutenberg Block Supports"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:88
msgid "Use this plugin with Gutenberg easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:93
msgid "Elementor Page Builder Support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:93
msgid "Use this plugin with Elementor easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:98
msgid "Bevear Builder Support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:98
msgid "Use this plugin with Bevear Builder easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:103
msgid "SiteOrigin Page Builder Support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:103
msgid "Use this plugin with SiteOrigin easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:108
msgid "Divi Page Builder Native Support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:108
msgid "Use this plugin with Divi Builder easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:113
msgid "Fusion Page Builder (Avada) native support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:113
msgid "Use this plugin with Fusion(Avada) Builder easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:118
#: includes/admin/wpsisac-how-it-work.php:159
msgid "WPBakery Page Builder Supports"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:118
msgid "Use this plugin with WPBakery Page Builder easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:123
msgid "Custom Read More link Text"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:123
msgid "Add custom name for read more link"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:128
msgid "Arrows design"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:128
msgid "Set arrows designs"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:133
#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Dots Design"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:133
msgid "Set dots designs"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:138
msgid "Display Slides for Particular Categories"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:138
msgid "Display only the slides with particular category"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:143
msgid "Exclude Some Slides"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:143
msgid "Do not display the slides you want"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:148
msgid "Exclude Some Categories"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:148
msgid "Do not display the slides for particular categories"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:153
msgid "Slides Order / Order By Parameters"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:153
msgid "Display slides according to date, title and etc"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:158
msgid "Multiple Slider Parameters"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:158
msgid "Slider parameters like autoplay, number of slide, sider dots and etc."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:163
msgid "Slider RTL Support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:163
msgid "Slider supports for RTL website"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:168
msgid "Automatic Update"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:168
msgid "Get automatic  plugin updates"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:169
#: includes/admin/settings/solution-features/basicpro-tab.php:170
msgid "Lifetime"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:173
msgid "Support"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:173
msgid "Get support for plugin"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:174
msgid "Limited"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:175
msgid "1 Year"
msgstr ""

#: includes/admin/settings/solution-features/solutions-features.php:111
msgid "Welcome"
msgstr ""

#: includes/admin/settings/solution-features/solutions-features.php:115
msgid "Pro Features"
msgstr ""

#: includes/admin/settings/solution-features/solutions-features.php:119
msgid "Basic Vs Pro"
msgstr ""

#: includes/admin/settings/solution-features/solutions-features.php:123
msgid "Slick Slider in Essential Bundle"
msgstr ""

#: includes/admin/settings/solution-features/solutions-features.php:127
msgid "Reviews"
msgstr ""

#: includes/admin/supports/blocks/gutenberg-block.php:75
msgid "Essential Plugin Blocks"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:35
msgid "How It Works - Display and Shortcode"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:43
msgid "Getting Started"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:47
msgid "Step-1. Go to \"Slick Slider --> Add Slide tab\"."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:48
msgid "Step-2. Add image title, description and images as a featured image"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:49
msgid "Step-3. Repeat this process for number of slides you want."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:50
msgid "Step-4. To display multiple slider, you can use category shortcode under \"Slick Slider--> Slider Category\""
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:57
msgid "How Shortcode Works"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:61
msgid "Step-1. Create a page like Slider OR add the shortcode in any page."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:62
msgid "Step-2. Put below shortcode as per your need."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:69
msgid "All Shortcodes"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:72
msgid "Slick slider Shortcode (design-1 to design-5)"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:73
msgid "Slick slider carousel Shortcode (design-1)"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:74
msgid "Slick slider carousel with center mode Shortcode (design-1)"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:75
msgid "Slick slider carousel with variable width Shortcode (design-1)"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:81
msgid "Documentation"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:84
msgid "Check Documentation"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:90
msgid "Demo"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:93
msgid "Check Free Demo"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:106
msgid "Gutenberg Support"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:114
msgid "How it Work"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:118
msgid "Step-1. Go to the Gutenberg editor of your page."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:119
msgid "Step-2. Search \"Slick Slider\" keyword in the Gutenberg block list."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:120
msgid "Step-3. Add any block of slick slider and you will find its relative options on the right end side."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:135
msgid "Help to improve this plugin!"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:139
msgid "Enjoyed this plugin? You can help by rate this plugin"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:139
msgid "5 stars!"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:149
msgid "Slick Slider Premium Features"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:153
msgid "90+ Predefined stunning designs"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:154
msgid "30 Image Slider Designs"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:155
msgid "30 Image Carousel and Center Slider Designs"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:156
msgid "33 Slider Variable width Designs"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:157
msgid "Drag & Drop order change"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:160
msgid "Elementor, Beaver and SiteOrigin Page Builder Support. "
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:161
msgid "Divi Page Builder Native Support."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:162
msgid "Fusion Page Builder (Avada) native support."
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:164
msgid "Custom CSS"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:165
msgid "Slider Center Mode Effect"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:166
msgid "Slider RTL support"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:167
msgid "Fully responsive"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:168
msgid "100% Multi language"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:170
msgid "Gain access to"
msgstr ""

#: includes/admin/wpsisac-how-it-work.php:171
msgid "Grab Slick Slider Now"
msgstr ""

#: includes/shortcodes/wpsisac-carousel.php:26
#: includes/shortcodes/wpsisac-carousel.php:34
msgid "Slick Carousel View"
msgstr ""

#: includes/shortcodes/wpsisac-slider.php:26
#: includes/shortcodes/wpsisac-slider.php:34
msgid "Slick Slider View"
msgstr ""

#: includes/wpsisac-function.php:123
#: includes/wpsisac-function.php:139
#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Design 1"
msgstr ""

#: includes/wpsisac-function.php:124
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Design 2"
msgstr ""

#: includes/wpsisac-function.php:125
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Design 3"
msgstr ""

#: includes/wpsisac-function.php:126
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Design 4"
msgstr ""

#: includes/wpsisac-function.php:127
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Design 5"
msgstr ""

#: includes/wpsisac-post-types.php:21
#: includes/wpsisac-post-types.php:33
msgid "Slick Slider"
msgstr ""

#: includes/wpsisac-post-types.php:22
msgid "slick slider"
msgstr ""

#: includes/wpsisac-post-types.php:23
msgid "All Slides"
msgstr ""

#: includes/wpsisac-post-types.php:24
msgid "Add Slide"
msgstr ""

#: includes/wpsisac-post-types.php:25
msgid "Add New slide"
msgstr ""

#: includes/wpsisac-post-types.php:26
msgid "Edit Slick Slider"
msgstr ""

#: includes/wpsisac-post-types.php:27
msgid "New Slick Slider"
msgstr ""

#: includes/wpsisac-post-types.php:28
msgid "View Slick Slider"
msgstr ""

#: includes/wpsisac-post-types.php:29
msgid "Search Slide"
msgstr ""

#: includes/wpsisac-post-types.php:30
msgid "No Slick Slider Items found"
msgstr ""

#: includes/wpsisac-post-types.php:31
msgid "No Slick Slider Items found in Trash"
msgstr ""

#: includes/wpsisac-post-types.php:34
msgid "Slide Image"
msgstr ""

#: includes/wpsisac-post-types.php:35
msgid "Set slide image"
msgstr ""

#: includes/wpsisac-post-types.php:36
msgid "Remove slide image"
msgstr ""

#: includes/wpsisac-post-types.php:37
msgid "Use as slide image"
msgstr ""

#: includes/wpsisac-post-types.php:69
#: includes/wpsisac-post-types.php:70
#: includes/wpsisac-post-types.php:79
msgid "Category"
msgstr ""

#: includes/wpsisac-post-types.php:71
msgid "Search Category"
msgstr ""

#: includes/wpsisac-post-types.php:72
msgid "All Category"
msgstr ""

#: includes/wpsisac-post-types.php:73
msgid "Parent Category"
msgstr ""

#: includes/wpsisac-post-types.php:74
msgid "Parent Category:"
msgstr ""

#: includes/wpsisac-post-types.php:75
msgid "Edit Category"
msgstr ""

#: includes/wpsisac-post-types.php:76
msgid "Update Category"
msgstr ""

#: includes/wpsisac-post-types.php:77
msgid "Add New Category"
msgstr ""

#: includes/wpsisac-post-types.php:78
msgid "New Category Name"
msgstr ""

#: templates/slider/design-2.php:25
#: templates/slider/design-3.php:24
#: templates/slider/design-4.php:22
#: templates/slider/design-5.php:22
msgid "Read More"
msgstr ""

#: wp-slick-image-slider.php:176
msgid "Thank you for activating %s"
msgstr ""

#: wp-slick-image-slider.php:177
msgid "It looks like you had PRO version %s of this plugin activated. To avoid conflicts the extra version has been deactivated and we recommend you delete it."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Descending"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Ascending"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Select Lazyload"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Ondemand"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Progressive"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Post Date"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Post Modified Date"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Post Title"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Post Slug"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Post ID"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Random"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Menu Order (Sort Order)"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Same Window"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "New Window"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "True"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "False"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "General Parameters"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Design"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Image Height"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Control height of the featured image. You can enter any numeric number. e.g 500. Leave empty for default height."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Image Fit"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Fill the post image in a whole container."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Media Size"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Choose WordPress registered image size. e.g thumbnail, medium, large, full."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Show Content"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Display Content. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Premium version"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "to get this option."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Show Read More"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Show/Hide read more links. Values are “true\" and “false\". Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Read More Text"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter read more text. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Link Behaviour"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Choose link bahaviour. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Slider Parameters"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Dots"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Arrows"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Show prev - next arrows."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Slide To Show"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Enter number for slide to show at a time."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Slide To Scroll"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Enter number for slide to scroll at a time."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Autoplay"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Autoplay Interval"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter autoplay interval speed."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Speed"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter slide speed."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Center Mode"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Enable centered view with partial prev/next slides. Use with odd numbered `Slides to Scroll` and `Slider Column` counts."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Variable Width"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Enable Variable width of images in slider. By default value us “false”"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Loop"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enable infinite loop for continuous sliding."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Pause On Hover"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Pause slider autoplay on hover."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Slider Lazyload"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Select option to use lazy loading in slider."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Choose dots pagination design. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Arrows Design"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Choose prev - next arrows design. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "Center Padding"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
msgid "You can set Center Padding in slider. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Pause On Focus"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Pause slider autoplay when slider element is focused. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Query Parameters"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Total items"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter number of slide to be displayed. Enter -1 to display all."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Display Specific Category"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "You can pass multiple ids with comma seperated. You can find id at relevant category listing page."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter category id to display categories wise."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Order By"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Display slides in your order. Values are “date\", “modified\", “title\" (Post Title), “ID\", “rand\" (Random), “menu_order\" (Sort Order). Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Order"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Controls slides order. Values are “ASC\" OR “DESC\". Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Display Child Category"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "If you are using parent category then whether to display child category or not. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Exclude Category"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Exclude post category. Works only if `Category` field is empty. You can pass multiple ids with comma seperated. You can find id at relevant category listing page. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Display Specific Posts"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter id of the post which you want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Exclude Post"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter id of the post which you do not want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Query Offset"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Exclude number of posts from starting. e.g if you pass 5 then it will skip first five post. Note: This will not work with limit=-1. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Do you want to check demo of this plugin."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Click here"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/index.js:1
#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Do you also want to check premium version demo of this plugin."
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Fade"
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enable fade effect instead of slide effect."
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Slider Nav Column"
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/index.js:1
msgid "Enter slider navigation columns. Only applicable to Design 4,5,6. Upgrade to "
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/block.json
msgctxt "block title"
msgid "Slick Carousel Slider"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/block.json
msgctxt "block description"
msgid "Display post in a carousel slider view with various layouts."
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/block.json
#: includes/admin/supports/blocks/build/slick-slider/block.json
msgctxt "block keyword"
msgid "essential"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/block.json
msgctxt "block keyword"
msgid "slick carousel slider"
msgstr ""

#: includes/admin/supports/blocks/build/slick-carousel-slider/block.json
msgctxt "block keyword"
msgid "slick-carousel-slider"
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/block.json
msgctxt "block title"
msgid "Slick Slider"
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/block.json
msgctxt "block description"
msgid "Display post in a slider view with various layouts."
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/block.json
msgctxt "block keyword"
msgid "slick slider"
msgstr ""

#: includes/admin/supports/blocks/build/slick-slider/block.json
msgctxt "block keyword"
msgid "slick-slider"
msgstr ""
