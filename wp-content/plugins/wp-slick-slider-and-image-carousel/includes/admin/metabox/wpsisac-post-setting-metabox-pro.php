<?php
/**
 * Function Custom meta box for Premium
 * 
 * @package WP Slick Slider and Image Carousel
 * @since 2.4.2
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

?>
<!-- <div class="pro-notice"><strong><?php // echo sprintf( __( 'Utilize this <a href="%s" target="_blank">Premium Features (With Risk-Free 30 days money back guarantee)</a> to get best of this plugin with Annual or Lifetime bundle deal.', 'wp-slick-slider-and-image-carousel'), WPSISAC_PLUGIN_LINK_UNLOCK); ?></strong></div> -->

<!-- <div class="wpsisac-black-friday-banner-wrp">
		<a href="<?php //echo esc_url( WPSISAC_PLUGIN_LINK_UNLOCK ); ?>" target="_blank"><img style="width: 100%;" src="<?php //echo esc_url( WPSISAC_URL ); ?>assets/images/black-friday-banner.png" alt="black-friday-banner" /></a>
</div> -->

	<strong style="color:#2ECC71; font-weight: 700;"><?php echo sprintf( __( ' <a href="%s" target="_blank" style="color:#2ECC71;">Upgrade To Pro</a> and Get Designs, Optimization, Security, Backup, Migration Solutions @ one stop.', 'countdown-timer-ultimate'), WPSISAC_PLUGIN_LINK_UNLOCK); ?></strong>

<!-- <div class="pro-notice">
	<strong>
		<?php //echo sprintf( __( 'Try All These <a href="%s" target="_blank">PRO with Early Back Friday Deals on lifetime plan FLAT $100 USD OFF.</a>', 'wp-slick-slider-and-image-carousel'), WPSISAC_PLUGIN_LINK_UNLOCK); ?>
	</strong>
</div> -->

<table class="form-table wpsisac-metabox-table">
	<tbody>

		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Layouts ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><strong><?php esc_html_e( '3 ( Slider, Carousel, Variable width ). In lite version only Slider and Carousel layout.', 'wp-slick-slider-and-image-carousel' ); ?></strong></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Designs ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><strong>30+</strong><?php esc_html_e( ' In lite version only 5+ design.', 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Arrows design ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><strong>7+</strong><?php esc_html_e( ' In lite version only one design.', 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Dots Design ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><strong>11+</strong><?php esc_html_e( ' In lite version only one design.', 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Navigation Support ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><?php esc_html_e( 'Thumbnail navigation support to some designs.', 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Custom Read More link Text ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><?php esc_html_e( 'Add a custom name for read more link', 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'WP Templating Features ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><?php esc_html_e( 'You can modify plugin html/designs in your current theme.', 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Shortcode Generator ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><?php esc_html_e( 'Play with all shortcode parameters with preview panel. No documentation required.' , 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Drag & Drop Slide Order Change ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><?php esc_html_e( 'Arrange your desired slides with your desired order and display.' , 'wp-slick-slider-and-image-carousel' ); ?></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Page Builder Support ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><strong><?php esc_html_e( 'Gutenberg Block, Elementor, Bevear Builder, SiteOrigin, Divi, Visual Composer and Fusion Page Builder Support', 'wp-slick-slider-and-image-carousel' ); ?></strong></span>
			</td>
		</tr>
		<tr class="wpsisac-pro-feature">
			<th>
				<?php esc_html_e( 'Exclude Slider Post and Exclude Some Categories ', 'wp-slick-slider-and-image-carousel' ); ?><span class="wpsisac-pro-tag"><?php esc_html_e( 'PRO','wp-slick-slider-and-image-carousel' );?></span>
			</th>
			<td>
				<span class="description"><strong><?php esc_html_e( 'Do not display the slider post & Do not display the slider category for particular categories.' , 'wp-slick-slider-and-image-carousel' ); ?></strong></span>
			</td>
		</tr>
	</tbody>
</table><!-- end .wpsisac-metabox-table -->