{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "wpsisac/slick-carousel-slider", "version": "1.0", "title": "Slick Carousel Slider", "category": "essp_guten_block", "icon": "slides", "description": "Display post in a carousel slider view with various layouts.", "example": {}, "supports": {"html": false, "align": true, "multiple": true}, "textdomain": "wp-slick-slider-and-image-carousel", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "keywords": ["essential", "slick carousel slider", "slick-carousel-slider"], "attributes": {"design": {"type": "string", "default": "design-1"}, "image_size": {"type": "string", "default": "full"}, "image_fit": {"type": "string", "default": "true"}, "sliderheight": {"type": "number", "default": ""}, "slidestoshow": {"type": "number", "default": 3}, "slidestoscroll": {"type": "number", "default": 1}, "dots": {"type": "string", "default": "true"}, "arrows": {"type": "string", "default": "true"}, "autoplay": {"type": "string", "default": "true"}, "autoplay_interval": {"type": "number", "default": 3000}, "speed": {"type": "number", "default": 300}, "loop": {"type": "string", "default": "true"}, "centermode": {"type": "string", "default": "false"}, "variablewidth": {"type": "string", "default": "false"}, "hover_pause": {"type": "string", "default": "true"}, "lazyload": {"type": "string", "default": ""}, "limit": {"type": "string", "default": -1}, "category": {"type": "string", "default": ""}, "align": {"type": "string", "default": ""}, "className": {"type": "string", "default": ""}}}