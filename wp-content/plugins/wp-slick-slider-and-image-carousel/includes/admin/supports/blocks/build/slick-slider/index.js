(()=>{"use strict";const e=window.wp.blocks,l=JSON.parse('{"UU":"wpsisac/slick-slider"}'),a=window.React,i=window.wp.i18n,s=window.wp.components,r=window.wp.blockEditor,t=(window.wp.element,[{value:"desc",label:(0,i.__)("Descending","wp-slick-slider-and-image-carousel")},{value:"asc",label:(0,i.__)("Ascending","wp-slick-slider-and-image-carousel")}]),n=[{value:"",label:(0,i.__)("Select Lazyload","wp-slick-slider-and-image-carousel")},{value:"ondemand",label:(0,i.__)("Ondemand","wp-slick-slider-and-image-carousel")},{value:"progressive",label:(0,i.__)("Progressive","wp-slick-slider-and-image-carousel")}],o=[{value:"date",label:(0,i.__)("Post Date","wp-slick-slider-and-image-carousel")},{value:"modified",label:(0,i.__)("Post Modified Date","wp-slick-slider-and-image-carousel")},{value:"title",label:(0,i.__)("Post Title","wp-slick-slider-and-image-carousel")},{value:"name",label:(0,i.__)("Post Slug","wp-slick-slider-and-image-carousel")},{value:"ID",label:(0,i.__)("Post ID","wp-slick-slider-and-image-carousel")},{value:"rand",label:(0,i.__)("Random","wp-slick-slider-and-image-carousel")},{value:"menu_order",label:(0,i.__)("Menu Order (Sort Order)","wp-slick-slider-and-image-carousel")}],c=[{value:"self",label:(0,i.__)("Same Window","wp-slick-slider-and-image-carousel")},{value:"blank",label:(0,i.__)("New Window","wp-slick-slider-and-image-carousel")}],d=[{value:"true",label:(0,i.__)("True","wp-slick-slider-and-image-carousel")},{value:"false",label:(0,i.__)("False","wp-slick-slider-and-image-carousel")}];(0,e.registerBlockType)(l.UU,{edit:function({attributes:e,setAttributes:l}){const p=e=>a=>l({[e]:a}),m=(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(s.PanelBody,{title:(0,i.__)("General Parameters","wp-slick-slider-and-image-carousel")},(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Design","wp-slick-slider-and-image-carousel"),value:e.design,options:[{value:"design-1",label:(0,i.__)("Design 1","wp-slick-slider-and-image-carousel")},{value:"design-2",label:(0,i.__)("Design 2","wp-slick-slider-and-image-carousel")},{value:"design-3",label:(0,i.__)("Design 3","wp-slick-slider-and-image-carousel")},{value:"design-4",label:(0,i.__)("Design 4","wp-slick-slider-and-image-carousel")},{value:"design-5",label:(0,i.__)("Design 5","wp-slick-slider-and-image-carousel")}],onChange:p("design")}),(0,a.createElement)(s.ToggleControl,{label:(0,i.__)("Show Content","wp-slick-slider-and-image-carousel"),checked:!!e.show_content,onChange:p("show_content")}),(0,a.createElement)(s.RangeControl,{label:(0,i.__)("Image Height","wp-slick-slider-and-image-carousel"),value:e.sliderheight,onChange:p("sliderheight"),min:0,max:5e3,help:(0,i.__)("Control height of the featured image. You can enter any numeric number. e.g 500. Leave empty for default height.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Image Fit","wp-slick-slider-and-image-carousel"),value:e.image_fit,options:d,onChange:p("image_fit"),help:(0,i.__)("Fill the post image in a whole container.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.TextControl,{label:(0,i.__)("Media Size","wp-slick-slider-and-image-carousel"),value:e.image_size,onChange:p("image_size"),help:(0,i.__)("Choose WordPress registered image size. e.g thumbnail, medium, large, full.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Show Read More","wp-slick-slider-and-image-carousel"),options:d})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)('Show/Hide read more links. Values are “true" and “false". Upgrade to ',"wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.TextControl,{label:(0,i.__)("Read More Text","wp-slick-slider-and-image-carousel")})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Enter read more text. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Link Behaviour","wp-slick-slider-and-image-carousel"),options:c})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Choose link bahaviour. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel")))),(0,a.createElement)(s.PanelBody,{title:(0,i.__)("Slider Parameters","wp-slick-slider-and-image-carousel"),initialOpen:!1},(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Dots","wp-slick-slider-and-image-carousel"),value:e.dots,options:d,onChange:p("dots")}),(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Arrows","wp-slick-slider-and-image-carousel"),value:e.arrows,options:d,onChange:p("arrows"),help:(0,i.__)("Show prev - next arrows.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Autoplay","wp-slick-slider-and-image-carousel"),value:e.autoplay,options:d,onChange:p("autoplay")}),"true"==e.autoplay&&(0,a.createElement)(s.RangeControl,{label:(0,i.__)("Autoplay Interval","wp-slick-slider-and-image-carousel"),value:e.autoplay_interval,onChange:p("autoplay_interval"),min:1,max:5e4,help:(0,i.__)("Enter autoplay interval speed.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.RangeControl,{label:(0,i.__)("Speed","wp-slick-slider-and-image-carousel"),value:e.speed,onChange:p("speed"),min:1,max:5e4,help:(0,i.__)("Enter slide speed.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Fade","wp-slick-slider-and-image-carousel"),value:e.fade,options:d,onChange:p("fade"),help:(0,i.__)("Enable fade effect instead of slide effect.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Loop","wp-slick-slider-and-image-carousel"),value:e.loop,options:d,onChange:p("loop"),help:(0,i.__)("Enable infinite loop for continuous sliding.","wp-slick-slider-and-image-carousel")}),"true"==e.autoplay&&(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Pause On Hover","wp-slick-slider-and-image-carousel"),value:e.hover_pause,options:d,onChange:p("hover_pause"),help:(0,i.__)("Pause slider autoplay on hover.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Slider Lazyload","wp-slick-slider-and-image-carousel"),value:e.lazyload,options:n,onChange:p("lazyload"),help:(0,i.__)("Select option to use lazy loading in slider.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Dots Design","wp-slick-slider-and-image-carousel"),options:[{value:"desc",label:(0,i.__)("Descending","wp-slick-slider-and-image-carousel")}]})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Choose dots pagination design. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Arrows Design","wp-slick-slider-and-image-carousel"),options:[{value:"self",label:(0,i.__)("Same Window","wp-slick-slider-and-image-carousel")}]})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Choose prev - next arrows design. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.RangeControl,{label:(0,i.__)("Slider Nav Column","wp-slick-slider-and-image-carousel")})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Enter slider navigation columns. Only applicable to Design 4,5,6. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Pause On Focus","wp-slick-slider-and-image-carousel"),options:d})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Pause slider autoplay when slider element is focused. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel")))),(0,a.createElement)(s.PanelBody,{title:(0,i.__)("Query Parameters","wp-slick-slider-and-image-carousel"),initialOpen:!1},(0,a.createElement)(s.RangeControl,{label:(0,i.__)("Total items","wp-slick-slider-and-image-carousel"),value:e.limit,onChange:p("limit"),min:-1,max:1e3,help:(0,i.__)("Enter number of slide to be displayed. Enter -1 to display all.","wp-slick-slider-and-image-carousel")}),(0,a.createElement)(s.TextControl,{label:(0,i.__)("Display Specific Category","wp-slick-slider-and-image-carousel"),value:e.category,onChange:p("category"),help:(0,a.createElement)("span",{title:(0,i.__)("You can pass multiple ids with comma seperated. You can find id at relevant category listing page.","wp-slick-slider-and-image-carousel")},(0,i.__)("Enter category id to display categories wise.","wp-slick-slider-and-image-carousel")," [?]")}),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Order By","wp-slick-slider-and-image-carousel"),options:o})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)('Display slides in your order. Values are “date", “modified", “title" (Post Title), “ID", “rand" (Random), “menu_order" (Sort Order). Upgrade to ',"wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Order","wp-slick-slider-and-image-carousel"),options:t})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)('Controls slides order. Values are “ASC" OR “DESC". Upgrade to ',"wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.SelectControl,{label:(0,i.__)("Display Child Category","wp-slick-slider-and-image-carousel"),options:d})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("If you are using parent category then whether to display child category or not. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.TextControl,{label:(0,i.__)("Exclude Category","wp-slick-slider-and-image-carousel")})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Exclude post category. Works only if `Category` field is empty. You can pass multiple ids with comma seperated. You can find id at relevant category listing page. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.TextControl,{label:(0,i.__)("Display Specific Posts","wp-slick-slider-and-image-carousel")})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Enter id of the post which you want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.TextControl,{label:(0,i.__)("Exclude Post","wp-slick-slider-and-image-carousel")})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Enter id of the post which you do not want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel"))),(0,a.createElement)(s.Disabled,null,(0,a.createElement)(s.RangeControl,{label:(0,i.__)("Query Offset","wp-slick-slider-and-image-carousel")})),(0,a.createElement)(s.__experimentalText,null,(0,a.createElement)("span",{className:"wpos-hidden-opts-desc"},(0,i.__)("Exclude number of posts from starting. e.g if you pass 5 then it will skip first five post. Note: This will not work with limit=-1. Upgrade to ","wp-slick-slider-and-image-carousel"),(0,a.createElement)(s.ExternalLink,{href:Wpsisac_free_Block.pro_link,target:"_blank"},(0,i.__)("Premium version","wp-slick-slider-and-image-carousel")),(0,i.__)("to get this option.","wp-slick-slider-and-image-carousel")))));var _=(e=>{var l=wp.blocks.getBlockAttributes("wpsisac/slick-slider"),a=["content_tail"],i="";for(var s in e)e[s]="string"==typeof e[s]?e[s].trim():e[s],e.hasOwnProperty(s)&&void 0!==e[s]&&e[s]!==l[s]&&(""!==e[s]||jQuery.inArray(s,a)>=0)&&(i+=s+'="'+e[s]+'" ');return i})(e),u=(_=_.trim())?"[slick-slider "+_+"]":"[slick-slider]";return(0,a.createElement)("div",{...(0,r.useBlockProps)()},m,(0,a.createElement)("div",{className:"wpos-pro-guten-shrt"},(0,a.createElement)("div",{className:"wpos-pro-guten-shrt-title"},(0,a.createElement)("span",null,(0,i.__)("WP Slick Slider and Image Carousel - Slider"))),u,(0,a.createElement)("div",{className:"wpos-guten-shrt-footer"},(0,a.createElement)("span",null),(0,i.__)("Do you want to check demo of this plugin.","wp-slick-slider-and-image-carousel")," ",(0,a.createElement)("a",{href:Wpsisac_free_Block.free_demo_link,target:"_blank"},(0,i.__)("Click here","wp-slick-slider-and-image-carousel"))),(0,a.createElement)("div",{className:"wpos-guten-shrt-footer"},(0,i.__)("Do you also want to check premium version demo of this plugin.","wp-slick-slider-and-image-carousel")," ",(0,a.createElement)("a",{href:Wpsisac_free_Block.pro_demo_link,target:"_blank"},(0,i.__)("Click here","wp-slick-slider-and-image-carousel")))))}})})();