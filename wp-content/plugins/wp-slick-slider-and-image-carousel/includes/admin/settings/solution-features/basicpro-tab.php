<?php
/**
 * Admin Class
 *
 * Handles the Admin side functionality of plugin
 *
 * @package WP Slick Slider and Image Carousel
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
} ?>
<div id="wpsisac_basic_tabs" class="wpsisac-vtab-cnt wpsisac_basic_tabs wpsisac-clearfix">
	
	<div class="wpsisac-black-friday-banner-wrp" style="background:#e1ecc8;padding: 20px 20px 40px; border-radius:5px; text-align:center;margin-bottom: 40px;">
		<h2 style="font-size:30px; margin-bottom:10px;"><span style="color:#0055fb;">WP Slick Slider and Image Carousel</span> is included in <span style="color:#0055fb;">Essential Plugin Bundle</span> </h2> 
		<h4 style="font-size: 18px;margin-top: 0px;color: #ff5d52;margin-bottom: 24px;">Now get Designs, Optimization, Security, Backup, Migration Solutions @ one stop. </h4>

		<div class="wpsisac-black-friday-feature">

			<div class="wpsisac-inner-deal-class" style="width:40%;">
				<div class="wpsisac-inner-Bonus-class">Bonus</div>
				<div class="wpsisac-image-logo" style="font-weight: bold;font-size: 26px;color: #222;"><img style="width: 34px; height:34px;vertical-align: middle;margin-right: 5px;" class="wpsisac-img-logo" src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/essential-logo-small.png" alt="essential-logo" /><span class="wpsisac-esstial-name" style="color:#0055fb;">Essential </span>Plugin</div>
				<div class="wpsisac-sub-heading" style="font-size: 16px;text-align: left;font-weight: bold;color: #222;margin-bottom: 10px;">Includes All premium plugins at no extra cost.</div>
				<a class="wpsisac-sf-btn" href="<?php echo esc_url( WPSISAC_PLUGIN_BUNDLE_LINK ); ?>" target="_blank">Grab The Deal</a>
			</div>

			<div class="wpsisac-main-list-class" style="width:60%;">
				<div class="wpsisac-inner-list-class">
					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/img-slider.png" alt="essential-logo" /> Image Slider</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/advertising.png" alt="essential-logo" /> Publication</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/marketing.png" alt="essential-logo" /> Marketing</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/photo-album.png" alt="essential-logo" /> Photo album</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/showcase.png" alt="essential-logo" /> Showcase</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/shopping-bag.png" alt="essential-logo" /> WooCommerce</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/performance.png" alt="essential-logo" /> Performance</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/security.png" alt="essential-logo" /> Security</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/forms.png" alt="essential-logo" /> Pro Forms</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/seo.png" alt="essential-logo" /> SEO</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/backup.png" alt="essential-logo" /> Backups</li></div>

					<div class="wpsisac-list-img-class"><img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/White-labeling.png" alt="essential-logo" /> Migration</li></div>
				</div>
			</div>
		</div>
		<div class="wpsisac-main-feature-item">
			<div class="wpsisac-inner-feature-item">
				<div class="wpsisac-list-feature-item">
					<img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/layers.png" alt="layer" />
					<h5>Site management</h5>
					<p>Manage, update, secure & optimize unlimited sites.</p>
				</div>
				<div class="wpsisac-list-feature-item">
					<img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/risk.png" alt="backup" />
					<h5>Backup storage</h5>
					<p>Secure sites with auto backups and easy restore.</p>
				</div>
				<div class="wpsisac-list-feature-item">
					<img src="<?php echo esc_url( WPSISAC_URL ); ?>assets/images/logo-image/support.png" alt="support" />
					<h5>Support</h5>
					<p>Get answers on everything WordPress at anytime.</p>
				</div>
			</div>
		</div>
		<a class="wpsisac-sf-btn" href="<?php echo esc_url( WPSISAC_PLUGIN_BUNDLE_LINK ); ?>" target="_blank">Grab The Deal</a>
	</div>
	
	<!-- <div class="wpsisac-black-friday-banner-wrp">
			<a href="<?php //echo esc_url( WPSISAC_PLUGIN_BUNDLE_LINK ); ?>" target="_blank"><img style="width: 100%;" src="<?php // echo esc_url( WPSISAC_URL ); ?>assets/images/black-friday-banner.png" alt="black-friday-banner" /></a>
	</div> -->

	<h3 class="wpsisac-basic-heading">Compare <span class="wpsisac-blue">"WP Slick Slider and Image Carousel"</span> Basic VS Pro</h3>

	<!-- <div class="wpsisac-deal-offer-wrap">
		<div class="wpsisac-deal-offer"> 
			<div class="wpsisac-inn-deal-offer">
				<h3 class="wpsisac-inn-deal-hedding"><span>Buy Slick Slider Pro</span> today and unlock all the powerful features.</h3>
				<h4 class="wpsisac-inn-deal-sub-hedding"><span style="color:red;">Extra Bonus: </span>Users will get <span>extra best discount</span> on the regular price using this coupon code.</h4>
			</div>
			<div class="wpsisac-inn-deal-offer-btn">
				<div class="wpsisac-inn-deal-code"><span>EPSEXTRA</span></div>
				<a href="<?php //echo esc_url(WPSISAC_PLUGIN_BUNDLE_LINK); ?>"  target="_blank" class="wpsisac-sf-btn wpsisac-sf-btn-orange"><span class="dashicons dashicons-cart"></span> Get Essential Bundle Now</a>
				<em class="risk-free-guarantee"><span class="heading">Risk-Free Guarantee </span> - We offer a <span>30-day money back guarantee on all purchases</span>. If you are not happy with your purchases, we will refund your purchase. No questions asked!</em>
			</div>
		</div>
	</div> -->

	<!-- <div class="wpsisac-deal-offer-wrap">
		<div class="wpsisac-deal-offer"> 
			<div class="wpsisac-inn-deal-offer">
				<h3 class="wpsisac-inn-deal-hedding"><span>Try Slick Slider Pro</span> in Essential Bundle Free For 5 Days.</h3>
			</div>
			<div class="wpsisac-deal-free-offer">
				<a href="<?php // echo esc_url( WPSISAC_PLUGIN_BUNDLE_LINK ); ?>" target="_blank" class="wpsisac-sf-free-btn"><span class="dashicons dashicons-cart"></span> Try Pro For 5 Days Free</a>
			</div>
		</div>
	</div> -->

	<table class="wpos-plugin-pricing-table">
		<colgroup></colgroup>
		<colgroup></colgroup>
		<colgroup></colgroup>
		<thead>
			<tr>
				<th></th>
				<th>
					<h2><?php esc_html_e('Free', 'wp-slick-slider-and-image-carousel'); ?></h2>
				</th>
				<th>
					<h2 class="wpos-epb" style="margin-bottom: 10px;"><?php esc_html_e('Premium', 'wp-slick-slider-and-image-carousel'); ?></h2>
				</th>
			</tr>
		</thead>
	   <tbody>
			<tr>
				<th><?php esc_html_e( 'Designs', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Designs that make your website better', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td>6</td>
				<td>90+</td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Shortcodes', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Shortcode provide output to the front-end side', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><?php esc_html_e( '2 (Slider, Carousel)', 'wp-slick-slider-and-image-carousel' ); ?></td>
				<td><?php esc_html_e( '3 (Slider, Carousel, Variable width )', 'wp-slick-slider-and-image-carousel' ); ?></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Shortcode Parameters', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Add extra power to the shortcode', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td>10</td>
				<td>30+</td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Shortcode Generator', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Play with all shortcode parameters with preview panel. No documentation required!!', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'WP Templating Features', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'You can modify plugin html/designs in your current theme.', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Drag &amp; Drop Slide Order Change', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Arrange your desired slides with your desired order and display', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Navigation Support', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Thumbnail navigation support to some designs', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Loop Control', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Infinite scroll control', 'wp-slick-slider-and-image-carousel' ); ?> </span></th>
				<td><i class="dashicons dashicons-yes"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Gutenberg Block Supports', 'wp-slick-slider-and-image-carousel' ); ?> <span><?php esc_html_e( 'Use this plugin with Gutenberg easily', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-yes"></i></td>
				<td><i class="dashicons dashicons-yes"></i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Elementor Page Builder Support', 'wp-slick-slider-and-image-carousel' ); ?> <em class="wpos-new-feature">New</em> <span><?php esc_html_e( 'Use this plugin with Elementor easily', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"></i></td>
				<td><i class="dashicons dashicons-yes"></i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Bevear Builder Support', 'wp-slick-slider-and-image-carousel' ); ?> <em class="wpos-new-feature">New</em> <span><?php esc_html_e( 'Use this plugin with Bevear Builder easily', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"></i></td>
				<td><i class="dashicons dashicons-yes"></i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'SiteOrigin Page Builder Support', 'wp-slick-slider-and-image-carousel' ); ?> <em class="wpos-new-feature">New</em> <span><?php esc_html_e( 'Use this plugin with SiteOrigin easily', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"></i></td>
				<td><i class="dashicons dashicons-yes"></i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Divi Page Builder Native Support', 'wp-slick-slider-and-image-carousel' ); ?> <em class="wpos-new-feature">New</em> <span><?php esc_html_e( 'Use this plugin with Divi Builder easily', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-yes"></i></td>
				<td><i class="dashicons dashicons-yes"></i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Fusion Page Builder (Avada) native support', 'wp-slick-slider-and-image-carousel' ); ?> <em class="wpos-new-feature">New</em> <span><?php esc_html_e( 'Use this plugin with Fusion(Avada) Builder easily', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-yes"></i></td>
				<td><i class="dashicons dashicons-yes"></i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'WPBakery Page Builder Supports', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Use this plugin with WPBakery Page Builder easily', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Custom Read More link Text', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Add custom name for read more link', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Arrows design', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Set arrows designs', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td>1</td>
				<td>8</td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Dots Design', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Set dots designs', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td>1</td>
				<td>12</td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Display Slides for Particular Categories', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Display only the slides with particular category', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-yes"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Exclude Some Slides', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Do not display the slides you want', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Exclude Some Categories', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Do not display the slides for particular categories', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Slides Order / Order By Parameters', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Display slides according to date, title and etc', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-no-alt"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Multiple Slider Parameters', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Slider parameters like autoplay, number of slide, sider dots and etc.', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-yes"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Slider RTL Support', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Slider supports for RTL website', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><i class="dashicons dashicons-yes"> </i></td>
				<td><i class="dashicons dashicons-yes"> </i></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Automatic Update', 'wp-slick-slider-and-image-carousel' ); ?> <span><?php esc_html_e( 'Get automatic  plugin updates', 'wp-slick-slider-and-image-carousel' ); ?> </span></th>
				<td><?php esc_html_e( 'Lifetime', 'wp-slick-slider-and-image-carousel' ); ?></td>
				<td><?php esc_html_e( 'Lifetime', 'wp-slick-slider-and-image-carousel' ); ?></td>
			</tr>
			<tr>
				<th><?php esc_html_e( 'Support', 'wp-slick-slider-and-image-carousel' ); ?> <span class="subtext"><?php esc_html_e( 'Get support for plugin', 'wp-slick-slider-and-image-carousel' ); ?></span></th>
				<td><?php esc_html_e( 'Limited', 'wp-slick-slider-and-image-carousel' ); ?></td>
				<td><?php esc_html_e( '1 Year', 'wp-slick-slider-and-image-carousel' ); ?></td>
			</tr>
		</tbody>
	</table>

	<!-- <div class="wpsisac-deal-offer-wrap">
		<div class="wpsisac-deal-offer"> 
			<div class="wpsisac-inn-deal-offer">
				<h3 class="wpsisac-inn-deal-hedding"><span>Buy Slick Slider Pro</span> today and unlock all the powerful features.</h3>
				<h4 class="wpsisac-inn-deal-sub-hedding"><span style="color:red;">Extra Bonus: </span>Users will get <span>extra best discount</span> on the regular price using this coupon code.</h4>
			</div>
			<div class="wpsisac-inn-deal-offer-btn">
				<div class="wpsisac-inn-deal-code"><span>EPSEXTRA</span></div>
				<a href="<?php // echo esc_url(WPSISAC_PLUGIN_BUNDLE_LINK); ?>"  target="_blank" class="wpsisac-sf-btn wpsisac-sf-btn-orange"><span class="dashicons dashicons-cart"></span> Get Essential Bundle Now</a>
				<em class="risk-free-guarantee"><span class="heading">Risk-Free Guarantee </span> - We offer a <span>30-day money back guarantee on all purchases</span>. If you are not happy with your purchases, we will refund your purchase. No questions asked!</em>
			</div>
		</div>
	</div> -->

	<!-- <div class="wpsisac-deal-offer-wrap">
		<div class="wpsisac-deal-offer"> 
			<div class="wpsisac-inn-deal-offer">
				<h3 class="wpsisac-inn-deal-hedding"><span>Try Slick Slider Pro</span> in Essential Bundle Free For 5 Days.</h3>
			</div>
			<div class="wpsisac-deal-free-offer">
				<a href="<?php echo esc_url( WPSISAC_PLUGIN_BUNDLE_LINK ); ?>" target="_blank" class="wpsisac-sf-free-btn"><span class="dashicons dashicons-cart"></span> Try Pro For 5 Days Free</a>
			</div>
		</div>
	</div> -->

	<!-- <div class="wpsisac-black-friday-banner-wrp">
			<a href="<?php // echo esc_url( WPSISAC_PLUGIN_BUNDLE_LINK ); ?>" target="_blank"><img style="width: 100%;" src="<?php // echo esc_url( WPSISAC_URL ); ?>assets/images/black-friday-banner.png" alt="black-friday-banner" /></a>
	</div> -->

</div>