<?php
/**
 * Single cooperation post template
 * 
 * @package ahkjbsc
 */

get_header();
?>

<?php if (have_posts()) : while (have_posts()) : the_post(); ?>

<div class="cooperation-single">
    <!-- 面包屑导航 -->
    <div class="cooperation-breadcrumb">
        <div class="container">
            <div class="ant-breadcrumb">
                <span class="ant-breadcrumb-link">
                    <a href="<?php echo home_url(); ?>">首页</a>
                </span>
                <span class="ant-breadcrumb-separator">/</span>
                <span class="ant-breadcrumb-link">
                    <a href="<?php echo get_post_type_archive_link('cooperation'); ?>">合作机构</a>
                </span>
                <span class="ant-breadcrumb-separator">/</span>
                <span class="ant-breadcrumb-link">
                    <span>机构详情</span>
                </span>
            </div>
        </div>
    </div>

    <!-- 机构详情内容 -->
    <div class="cooperation-content-wrapper">
        <div class="container">
            <div class="ant-row">
                <!-- 主内容区域 -->
                <div class="ant-col ant-col-18">
                    <div class="cooperation-main">
                        <!-- 机构头部信息 -->
                        <header class="cooperation-header">
                            <div class="cooperation-basic-info">
                                <?php if (has_post_thumbnail()) : ?>
                                <div class="cooperation-logo">
                                    <?php the_post_thumbnail('large', array('class' => 'logo-image')); ?>
                                </div>
                                <?php endif; ?>
                                
                                <div class="cooperation-info">
                                    <h1 class="cooperation-title"><?php the_title(); ?></h1>
                                    
                                    <?php 
                                    $organ_types = get_the_terms(get_the_ID(), 'organ_type');
                                    if ($organ_types && !is_wp_error($organ_types)) :
                                    ?>
                                    <div class="cooperation-types">
                                        <?php foreach ($organ_types as $type) : ?>
                                        <span class="type-badge"><?php echo esc_html($type->name); ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="cooperation-meta-info">
                                        <?php 
                                        $create_date = get_field('create_date');
                                        $enter_date = get_field('enter_date');
                                        $person = get_field('person');
                                        ?>
                                        
                                        <?php if ($create_date) : ?>
                                        <div class="meta-item">
                                            <i class="anticon anticon-calendar"></i>
                                            <span class="meta-label">成立时间：</span>
                                            <span class="meta-value"><?php echo date('Y年m月', strtotime($create_date)); ?></span>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($enter_date) : ?>
                                        <div class="meta-item">
                                            <i class="anticon anticon-home"></i>
                                            <span class="meta-label">入驻时间：</span>
                                            <span class="meta-value"><?php echo date('Y年m月d日', strtotime($enter_date)); ?></span>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($person) : ?>
                                        <div class="meta-item">
                                            <i class="anticon anticon-team"></i>
                                            <span class="meta-label">企业人数：</span>
                                            <span class="meta-value"><?php echo $person; ?>人</span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </header>
                        
                        <!-- 机构详细内容 -->
                        <div class="cooperation-content">
                            <?php if (get_the_content()) : ?>
                            <div class="content-section">
                                <h3 class="section-title">
                                    <i class="anticon anticon-file-text"></i>
                                    机构介绍
                                </h3>
                                <div class="section-content">
                                    <?php the_content(); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php
                            // 获取所有自定义字段
                            $fields = get_fields();
                            if ($fields) :
                            ?>
                            <div class="content-section">
                                <h3 class="section-title">
                                    <i class="anticon anticon-info-circle"></i>
                                    详细信息
                                </h3>
                                <div class="section-content">
                                    <div class="info-grid">
                                        <?php foreach ($fields as $key => $value) : 
                                            if (in_array($key, ['create_date', 'enter_date', 'person'])) continue;
                                            if (empty($value)) continue;
                                        ?>
                                        <div class="info-item">
                                            <div class="info-label"><?php echo esc_html(str_replace('_', ' ', ucfirst($key))); ?>：</div>
                                            <div class="info-value"><?php echo esc_html($value); ?></div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- 侧边栏 -->
                <div class="ant-col ant-col-6">
                    <aside class="cooperation-sidebar">
                        
                        <!-- 相关机构 -->
                        <div class="sidebar-widget related-widget">
                            <h4 class="widget-title">相关机构</h4>
                            <div class="related-cooperations">
                                <?php
                                $related_args = array(
                                    'post_type' => 'cooperation',
                                    'posts_per_page' => 5,
                                    'post__not_in' => array(get_the_ID()),
                                    'orderby' => 'rand',
                                    'post_status' => 'publish'
                                );
                                
                                // 如果有机构类型，优先显示同类型机构
                                if ($organ_types && !is_wp_error($organ_types)) {
                                    $related_args['tax_query'] = array(
                                        array(
                                            'taxonomy' => 'organ_type',
                                            'field'    => 'term_id',
                                            'terms'    => wp_list_pluck($organ_types, 'term_id'),
                                        ),
                                    );
                                }
                                
                                $related_cooperations = new WP_Query($related_args);
                                
                                if ($related_cooperations->have_posts()) :
                                    while ($related_cooperations->have_posts()) : $related_cooperations->the_post();
                                ?>
                                <div class="related-cooperation-item">
                                    <div class="related-cooperation-content">
                                        <?php if (has_post_thumbnail()) : ?>
                                        <div class="related-cooperation-thumb">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('thumbnail', array('class' => 'related-thumb')); ?>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                        <div class="related-cooperation-info">
                                            <h6 class="related-cooperation-title">
                                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                            </h6>
                                            <div class="related-cooperation-meta">
                                                <?php 
                                                $related_person = get_field('person');
                                                if ($related_person) :
                                                ?>
                                                <span class="related-meta"><?php echo $related_person; ?>人</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php 
                                    endwhile;
                                    wp_reset_postdata();
                                else :
                                ?>
                                <p class="no-related">暂无相关机构</p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- 统计信息 -->
                        <div class="sidebar-widget stats-widget">
                            <h4 class="widget-title">平台统计</h4>
                            <div class="stats-info">
                                <?php
                                $total_cooperations = wp_count_posts('cooperation')->publish;
                                $total_categories = wp_count_terms('organ_type');
                                ?>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $total_cooperations; ?></div>
                                    <div class="stat-label">合作机构</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $total_categories; ?></div>
                                    <div class="stat-label">机构类型</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">98%</div>
                                    <div class="stat-label">满意度</div>
                                </div>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript 功能 -->
<script>

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    const elements = document.querySelectorAll('.cooperation-main > *');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            el.style.transition = 'all 0.6s ease-out';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php endwhile; endif; ?>

<?php
get_footer();
?>
