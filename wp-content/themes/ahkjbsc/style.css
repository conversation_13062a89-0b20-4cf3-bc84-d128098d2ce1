/*
Theme Name: 安徽科技大市场
Theme URI: https://www.ahcxg.cn/
Author: 安徽科技大市场
Author URI: https://www.ahcxg.cn/
Description: 安徽科技大市场
Version: 1.0.0
Text Domain: ahkjbsc-old
Domain Path: /languages/
Tags: 安徽科技大市场
 */
/* http://meyerweb.com/eric/tools/css/reset/
   v2.0 | 20110126
   License: none (public domain)
*/

body {
    min-width: 1200px;
    font-family: Microsoft YaHei
}

a {
    color: #3a69df;
    text-decoration: none;
    background-color: transparent;
    outline: none;
    cursor: pointer;
    -webkit-transition: color .3s;
    transition: color .3s;
    -webkit-text-decoration-skip: objects;
}

.tal {
    text-align: left
}

.tac {
    text-align: center
}

.tar {
    text-align: right
}

.cup {
    cursor: pointer
}

.app-wrapper {
    height: 100%
}

.container {
    width: 1160px;
    margin: 0 auto;
}

.app-wrapper > .ant-layout {
    overflow-y: auto;
    height: 100%
}

.basic-layout .ant-layout-content {
    min-height: auto
}

.basic-layout .page-container {
    margin-bottom: 20px;
    min-height: 100%;
}


.global-header {
    height: 30px;
    padding: 0;
    font-size: 12px;
    line-height: 30px;
    background: #e1e1e1
}

.header-search {
    background-color: #f5f5f6;
    min-height: 92px
}

.header-home {
    color: #fff;
    min-height: 92px;
    background: #1c4abd url(/wp-content/themes/ahkjbsc/assets/img/bg-header2.png) 50% no-repeat
}

.header-home .logo-wrapper h1 {
    color: #fff
}

.logo-wrapper {
    padding: 25px 0
}

.logo-wrapper h1 {
    display: inline-block;
    margin-bottom: 0;
    margin-left: 16px;
    vertical-align: middle;
    font-size: 18px;
    font-weight: 700;
    color: #3a69df
}

.top-menu {
    height: 48px;
    padding-top: 4px;
    padding-bottom: 4px;
    background-color: #3d6fee
}

.top-menu .ant-menu-horizontal {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: space-evenly;
    -ms-flex-pack: space-evenly;
    justify-content: space-evenly;
    line-height: 38px;
    color: #fff;
    border-bottom-color: #3d6fee;
    background-color: #3d6fee
}

.top-menu .ant-menu-item {
    padding: 0 30px
}

.top-menu .ant-menu-submenu-title {
    padding: 0 20px;
    -webkit-transition: none;
    transition: none
}

.top-menu .ant-menu-horizontal > .ant-menu-item > a, .top-menu .ant-menu-submenu-title:hover {
    color: #fff
}

.top-menu .ant-menu-horizontal > .ant-menu-item-active, .top-menu .ant-menu-horizontal > .ant-menu-item-open, .top-menu .ant-menu-horizontal > .ant-menu-item-selected, .top-menu .ant-menu-horizontal > .ant-menu-item:hover {
    position: relative;
    color: #fff;
    border-bottom: 2px solid transparent
}

.top-menu .ant-menu-horizontal > .ant-menu-item-active:after, .top-menu .ant-menu-horizontal > .ant-menu-item-open:after, .top-menu .ant-menu-horizontal > .ant-menu-item-selected:after, .top-menu .ant-menu-horizontal > .ant-menu-item:hover:after {
    position: absolute;
    right: 25px;
    bottom: -2px;
    left: 24px;
    height: 2px;
    content: "";
    background: #fff
}

.top-menu .ant-menu-horizontal > .ant-menu-submenu {
    -webkit-transition: none;
    transition: none
}

.top-menu .ant-menu-horizontal > .ant-menu-submenu-active, .top-menu .ant-menu-horizontal > .ant-menu-submenu-open, .top-menu .ant-menu-horizontal > .ant-menu-submenu-selected, .top-menu .ant-menu-horizontal > .ant-menu-submenu:hover {
    position: relative;
    color: #fff;
    border-bottom: 2px solid #123eac;
    background-color: #123eac
}

.top-menu .ant-menu-horizontal > .ant-menu-submenu-active:after, .top-menu .ant-menu-horizontal > .ant-menu-submenu-active:before, .top-menu .ant-menu-horizontal > .ant-menu-submenu-open:after, .top-menu .ant-menu-horizontal > .ant-menu-submenu-open:before, .top-menu .ant-menu-horizontal > .ant-menu-submenu-selected:after, .top-menu .ant-menu-horizontal > .ant-menu-submenu-selected:before, .top-menu .ant-menu-horizontal > .ant-menu-submenu:hover:after, .top-menu .ant-menu-horizontal > .ant-menu-submenu:hover:before {
    position: absolute;
    left: 0;
    width: 100%;
    height: 4px;
    content: "";
    background: #123eac
}

.top-menu .ant-menu-horizontal > .ant-menu-submenu-active:before, .top-menu .ant-menu-horizontal > .ant-menu-submenu-open:before, .top-menu .ant-menu-horizontal > .ant-menu-submenu-selected:before, .top-menu .ant-menu-horizontal > .ant-menu-submenu:hover:before {
    top: -4px
}

.top-menu .ant-menu-horizontal > .ant-menu-submenu-active:after, .top-menu .ant-menu-horizontal > .ant-menu-submenu-open:after, .top-menu .ant-menu-horizontal > .ant-menu-submenu-selected:after, .top-menu .ant-menu-horizontal > .ant-menu-submenu:hover:after {
    bottom: -6px
}

* {
    margin: 0;
    padding: 0;
    list-style: none;
}

img {
    border: 0;
}

a {
    text-decoration: none;
    color: #333;
}

.slideBox {
    width: 500px;
    height: 280px;
    overflow: hidden;
    position: relative;
    border: 1px solid #ddd;
}

.slideBox .hd {
    height: 15px;
    overflow: hidden;
    position: absolute;
    right: 10px;
    bottom: 10px;
    z-index: 1;
}

.slideBox .hd ul {
    overflow: hidden;
    zoom: 1;
    float: left;
}

.slideBox .hd ul li {
    float: left;
    margin-right: 2px;
    width: 15px;
    height: 15px;
    line-height: 14px;
    text-align: center;
    background: #fff;
    cursor: pointer;
}

.slideBox .hd ul li.on {
    background: #f00;
    color: #fff;
}

.slideBox .bd {
    position: relative;
    height: 100%;
    z-index: 0;
}

.slideBox .bd img {
    width: 500px;
    height: 280px;
}

.curSlide {
    border: 1px solid #ccc;
    margin: 10px 0;
    padding-left: 10px;
    background: #f6f6f6;
}

.curSlide span {
    color: #f00;
}

.news .process {
    padding: 60px 0;
    background: url(/wp-content/themes/ahkjbsc/assets/img/store_bg.png) no-repeat 50%;
    background-size: 100% 100%
}

.news .process .processWrap {
    margin: 0 auto
}

.actTitle .name {
    font-size: 32px;
    color: #121834;
    line-height: 32px;
    font-weight: 500;
    padding-bottom: 8px
}

.actTitle .desc {
    font-size: 12px;
    color: #000;
    line-height: 12px;
    font-weight: 400
}

.news .top {
    margin: 20px 0 0;
    padding-top: 10px;
    display: flex;
    background-image: url(/wp-content/themes/ahkjbsc/assets/img/liveProcess.png);
    background-repeat: no-repeat;
    background-size: 570px 142px;
    background-position: 100%;
    background-position-y: -10px
}

.news .process .processWrap {
    width: 1160px;
    margin: 0 auto;
}

.top .imgBox {
    background-color: rgba(108, 127, 163, .1);
    width: 500px;
    height: 280px
}

.news .ant-tabs {
    background-color: #fff;
    margin-right: 10px;
    height: 280px;
    width: 100%;
}

.news .ant-tabs-nav {
    margin: unset;
    background: #fff;
    border-radius: 2px;
    padding: 0 32px
}
.news .ant-tabs-nav:before {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    content: "";
    border-bottom: 1px solid #edeff2;
}

.news .ant-tabs-tab {
    font-size: 16px;
    color: #383b40;
    padding: 18px 12px 12px;
    line-height: 22px
}

.news .ant-tabs-tab + .ant-tabs-tab {
    margin: 0;
}

.news .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #2374ff;
    font-size: 18px;
    font-weight: 700
}

.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-ink-bar, .ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-ink-bar, .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar, .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar {
    height: 3px;
    background-color: transparent
}

.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-ink-bar:after, .ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-ink-bar:after, .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar:after, .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar:after {
    content: " ";
    display: block;
    margin: 0 auto;
    height: 3px;
    width: 84px;
    background-color: #2374ff;
    border-radius: 1px
}

.ant-tabs-nav:before {
    border-bottom: 1px solid #edeff2
}

.news .ant-tabs-content {
    display: block;
    height: 228px;
    width: 100%;
    background: #fff;
}

.news .ant-tabs-content ul {
    padding: 22px 32px !important;
}

.news .ant-tabs-content ul li {
    display: flex;
    justify-content: space-between;
    margin-bottom: 17px;
}

.page-home {}
.page-home .content {
    background: #fff;
}


.tab {
    border-bottom: 1px solid #edeff2
}

.tab .ant-tabs-tab {
    margin-right: 40px;
    padding: 16px 0
}

.tab .ant-tabs-tab-btn {
    font-size: 16px;
    color: #121834;
    line-height: 16px
}

.tab .ant-tabs-tab-active {
    font-weight: 700
}

.tab .ant-tabs-nav {
    width: 1200px;
    margin: 0 auto;
    padding-left: 30px
}

.tab .ant-tabs-bottom>.ant-tabs-nav:before,.tab .ant-tabs-bottom>div>.ant-tabs-nav:before,.tab .ant-tabs-top>.ant-tabs-nav:before,.tab .ant-tabs-top>div>.ant-tabs-nav:before {
    border-bottom: 0
}

.main {
    width: 1200px;
    margin: 0 auto
}

.itemBox {
    width: 1200px;
    padding: 0 20px
}

.itemBox:hover {
    border: 1px solid #eff0f3;
    box-shadow: 0 6px 16px 0 rgba(0,0,0,.08)
}

.itemBox:hover .signupItem {
    border-bottom: 0
}

.itemBox:hover .signupItem .title {
    color: #2374ff
}

.signupItem {
    padding: 20px 0;
    border-bottom: 1px solid #edeff2
}

.noEnd {
    color: #525a68
}

.isEnd {
    color: #8f97a3
}

.fixed-tooltip {
    padding-right: 0;
    padding-left: 8px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    background-color: #3d6fee
}

.fixed-tooltip .ant-tooltip-arrow {
    display: none
}

.fixed-tooltip .ant-tooltip-inner {
    margin-right: -4px;
    padding-right: 8px;
    line-height: 28px;
    border-radius: 0;
    background-color: #3d6fee;
    -webkit-box-shadow: none;
    box-shadow: none
}

.fixed-sidebar-wrap {
    position: fixed;
    width: 1160px;
    bottom: 0;
    left: 50%;
    z-index: 200;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.fixed-sidebar {
    position: absolute;
    z-index: 10;
    right: -60px;
    bottom: 50px
}

.fixed-sidebar .ant-back-top {
    position: static
}

.fixed-sidebar .ant-back-top:hover .ant-back-top-content {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    background-color: #3d6fee
}

.fixed-sidebar .ant-back-top-content {
    position: relative;
    border-radius: 4px;
    background: #b1b7c2
}

.fixed-sidebar .menu-icon {
    display: block;
    width: 40px;
    height: 40px;
    margin-top: 2px;
    cursor: pointer;
    -webkit-transition: all .3s cubic-bezier(.645, .045, .355, 1);
    transition: all .3s cubic-bezier(.645, .045, .355, 1);
    text-align: center;
    line-height: 40px;
    color: #fff;
    border-radius: 4px;
    background-color: #b1b7c2
}

.fixed-sidebar .menu-icon:hover {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    background-color: #3d6fee
}

@media screen and (max-width: 1200px) {
    .fixed-sidebar {
        right: 20px
    }
}

.global-footer {
    padding: 20px 0;
    color: #efefef;
    background: #2e3033;
    overflow: hidden;
}

.global-footer .qr-code {
    width: 50px;
    height: 50px
}

.global-footer .qr-code:hover {
    -webkit-transform: scale(3) translateX(-8px) translateY(-10px);
    transform: scale(3) translateX(-8px) translateY(-10px)
}

.global-footer h6 {
    margin-bottom: 0;
    font-size: 16px;
    color: #fff
}

.global-footer ul {
    margin-bottom: 0;
    padding-left: 0;
    list-style: none
}

.global-footer li {
    display: inline-block
}

.global-footer li + li {
    margin-left: 65px
}

.global-footer a {
    color: #fff
}

.page-news {
    background: #fff;
}
.page-news .ant-tabs-tab-active{
    border-bottom: 3px solid #2374ff;
}
.content {
    font-size: 18px;
}


.page-home-newsDetails {
    background-color: #fff;
    margin-bottom: 30px;
    padding: 20px;
    border-radius: 10px
}

.page-home-newsDetails .page-newsDetails-head .page-newsDetails-title {
    font-size: 16px;
    line-height: 58px;
    padding-top: 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.page-home-newsDetails .page-newsDetails-head .page-newsDetails-title h2 {
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center
}

.page-home-newsDetails .page-newsDetails-head .page-newsDetails-time {
    color: #999;
    height: 50px;
    margin-bottom: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid #e6e6fa
}

.page-home-newsDetails .page-newsDetails-head .page-newsDetails-time div span {
    margin: 0 10px
}

.page-home-newsDetails .page-newsDetails-body {
    padding: 20px 50px
}
