<?php
/*
Template Name: 新闻模板
Template Post Type: page
 */
get_header();
?>
    <div class="page-news page-container">
        <div class="content" style="padding: 30px 0;">
            <!-- 搜索和筛选区域 -->
            <div class="tab">
                <div class="ant-tabs ant-tabs-top">
                    <div role="tablist" class="ant-tabs-nav">
                        <div class="ant-tabs-nav-wrap">
                            <div class="ant-tabs-nav-list">
                                <div class="ant-tabs-tab <?php if (get_query_var('cat') == '') { echo 'ant-tabs-tab-active'; }?>" data-id="all">
                                    <div role="tab" class="ant-tabs-tab-btn" >全部新闻</div>
                                </div>
                                <?php
                                $categories = get_categories(array(
                                    'orderby' => 'name',
                                    'order' => 'ASC',
                                    'hide_empty' => true,
                                ));
                                foreach ($categories as $category) {
                                    echo '<div class="ant-tabs-tab ';
                                    if ($category->term_id == get_query_var('cat')) {
                                        echo'ant-tabs-tab-active';
                                    }
                                     echo '" data-id="'. $category->term_id. '">';
                                    echo '<div role="tab" class="ant-tabs-tab-btn" tabindex="0">';
                                    echo $category->name;
                                    echo '</div>';
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="container">
                <div class="news-filters" style="margin: 30px 0;">
                    <div class="ant-row">
                        <div class="ant-col ant-col-12">
                            <div class="ant-input-group ant-input-search ant-input-search-enter-button">
                                <input type="text" id="news-search" class="ant-input" placeholder="搜索新闻标题...">
                                <span class="ant-input-group-addon">
                                    <button class="ant-btn ant-btn-primary" type="button" onclick="searchNews()">
                                        <svg viewBox="64 64 896 896" focusable="false" data-icon="search" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg>
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文章列表 -->
                <div class="news-list">
                    <div class="ant-list ant-list-vertical ant-list-lg">
                        <?php
                        // 获取分页参数
                        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

                        // 获取分类筛选
                        $category_id = isset($_GET['cat']) ? intval($_GET['cat']) : '';

                        // 获取搜索关键词
                        $search_query = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

                        // 构建查询参数
                        $args = array(
                            'post_type' => 'post',
                            'posts_per_page' => 3, // 每页显示10篇文章
                            'paged' => $paged,
                            'post_status' => 'publish',
                            'orderby' => 'date',
                            'order' => 'DESC'
                        );

                        // 添加分类筛选
                        if (!empty($category_id)) {
                            $args['cat'] = $category_id;
                        }

                        // 添加搜索功能
                        if (!empty($search_query)) {
                            $args['s'] = $search_query;
                        }

                        // 执行查询
                        $news_query = new WP_Query($args);

                        if ($news_query->have_posts()) :
                            while ($news_query->have_posts()) : $news_query->the_post();
                                get_template_part('content', 'list');
                            endwhile;
                        else :
                            ?>
                            <div class="ant-empty">
                                <div class="ant-empty-image">
                                    <svg width="64" height="41" viewBox="0 0 64 41">
                                        <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                                            <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7"></ellipse>
                                            <g fill-rule="nonzero" stroke="#D9D9D9">
                                                <path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.258L9 12.76V22h46v-9.24z"></path>
                                                <path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                                      fill="#FAFAFA"></path>
                                            </g>
                                        </g>
                                    </svg>
                                </div>
                                <p class="ant-empty-description">暂无数据</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 分页 -->
                <?php if ($news_query->max_num_pages > 1) : ?>
                    <div class="news-pagination" style="margin-top: 40px; text-align: center;">
                        <div class="ant-pagination">
                            <?php
                            $pagination_args = array(
                                'total' => $news_query->max_num_pages,
                                'current' => $paged,
                                'format' => '?paged=%#%',
                                'show_all' => false,
                                'end_size' => 1,
                                'mid_size' => 2,
                                'prev_next' => true,
                                'prev_text' => '上一页',
                                'next_text' => '下一页',
                                'type' => 'array'
                            );

                            $pagination_links = paginate_links($pagination_args);

                            if ($pagination_links) {
                                foreach ($pagination_links as $link) {
                                    // 添加 Ant Design 样式类
                                    $link = str_replace('page-numbers', 'ant-pagination-item', $link);
                                    $link = str_replace('current', 'ant-pagination-item-active', $link);
                                    echo $link;
                                }
                            }
                            ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php wp_reset_postdata(); ?>
            </div>
        </div>
    </div>

    <script>
        (function($){
            $('.ant-tabs-tab').click(function() {

                filterByCategory($(this).data('id'));
            });
        })(jQuery);

        function searchNews() {
            var activeTab = document.querySelector('.ant-tabs-tab-active');
            const searchTerm = document.getElementById('news-search').value;
            const categoryFilter = activeTab.getAttribute('data-id');
            let url = window.location.pathname + '?';

            if (searchTerm) {
                url += 's=' + encodeURIComponent(searchTerm) + '&';
            }

            if (categoryFilter) {
                url += 'cat=' + categoryFilter + '&';
            }

            // 移除末尾的 & 或 ?
            url = url.replace(/[&?]$/, '');

            window.location.href = url;
        }

        function filterByCategory(id) {
            var activeTab = document.querySelector('.ant-tabs-tab-active');
            const searchTerm = document.getElementById('news-search').value;
            const categoryFilter = id || activeTab.getAttribute('data-id');
            let url = window.location.pathname + '?';

            if (categoryFilter !== 'all') {
                url += 'cat=' + categoryFilter + '&';
            }else {
                url = '<?php echo get_page_uri(111); ?>';
            }

            if (searchTerm) {
                url += 's=' + encodeURIComponent(searchTerm) + '&';
            }

            // 移除末尾的 & 或 ?
            url = url.replace(/[&?]$/, '');
            
            console.log(`url`, url);

            window.location.href = url;
        }

        // 回车键搜索
        document.getElementById('news-search').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                searchNews();
            }
        });

        // 保持搜索和筛选状态
        document.addEventListener('DOMContentLoaded', function () {
            const urlParams = new URLSearchParams(window.location.search);
            const searchTerm = urlParams.get('s');

            if (searchTerm) {
                document.getElementById('news-search').value = searchTerm;
            }
        });
    </script>

<?php
get_footer();