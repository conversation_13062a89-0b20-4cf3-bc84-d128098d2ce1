<?php
/**
 * 合作机构自定义字段示例
 * 这个文件展示了如何为cooperation自定义文章类型添加字段
 * 
 * 注意：这只是示例代码，实际项目中应该使用ACF插件或其他字段管理工具
 * 
 * @package ahkjbsc
 */

// 如果你使用ACF插件，可以通过以下方式注册字段组：

/*
// ACF字段组配置示例
if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
    'key' => 'group_cooperation_fields',
    'title' => '合作机构信息',
    'fields' => array(
        array(
            'key' => 'field_create_date',
            'label' => '成立时间',
            'name' => 'create_date',
            'type' => 'date_picker',
            'instructions' => '请选择机构成立时间',
            'required' => 0,
            'display_format' => 'Y-m-d',
            'return_format' => 'Y-m-d',
        ),
        array(
            'key' => 'field_enter_date',
            'label' => '入驻时间',
            'name' => 'enter_date',
            'type' => 'date_picker',
            'instructions' => '请选择机构入驻时间',
            'required' => 0,
            'display_format' => 'Y-m-d',
            'return_format' => 'Y-m-d',
        ),
        array(
            'key' => 'field_person',
            'label' => '企业人数',
            'name' => 'person',
            'type' => 'number',
            'instructions' => '请输入企业人数',
            'required' => 0,
            'min' => 1,
            'max' => 10000,
        ),
        array(
            'key' => 'field_contact_phone',
            'label' => '联系电话',
            'name' => 'contact_phone',
            'type' => 'text',
            'instructions' => '请输入联系电话',
            'required' => 0,
        ),
        array(
            'key' => 'field_contact_email',
            'label' => '联系邮箱',
            'name' => 'contact_email',
            'type' => 'email',
            'instructions' => '请输入联系邮箱',
            'required' => 0,
        ),
        array(
            'key' => 'field_address',
            'label' => '机构地址',
            'name' => 'address',
            'type' => 'textarea',
            'instructions' => '请输入机构地址',
            'required' => 0,
            'rows' => 3,
        ),
        array(
            'key' => 'field_website',
            'label' => '官方网站',
            'name' => 'website',
            'type' => 'url',
            'instructions' => '请输入官方网站地址',
            'required' => 0,
        ),
        array(
            'key' => 'field_business_scope',
            'label' => '经营范围',
            'name' => 'business_scope',
            'type' => 'textarea',
            'instructions' => '请输入经营范围',
            'required' => 0,
            'rows' => 4,
        ),
        array(
            'key' => 'field_cooperation_status',
            'label' => '合作状态',
            'name' => 'cooperation_status',
            'type' => 'select',
            'instructions' => '请选择合作状态',
            'required' => 0,
            'choices' => array(
                'active' => '正在合作',
                'pending' => '待合作',
                'completed' => '合作完成',
                'suspended' => '暂停合作',
            ),
            'default_value' => 'active',
        ),
    ),
    'location' => array(
        array(
            array(
                'param' => 'post_type',
                'operator' => '==',
                'value' => 'cooperation',
            ),
        ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
));

endif;
*/

// 如果你使用原生WordPress自定义字段，可以使用以下代码：

// 添加自定义字段元框
function add_cooperation_meta_boxes() {
    add_meta_box(
        'cooperation_details',
        '机构详细信息',
        'cooperation_meta_box_callback',
        'cooperation',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_cooperation_meta_boxes');

// 元框回调函数
function cooperation_meta_box_callback($post) {
    // 添加nonce字段
    wp_nonce_field('cooperation_meta_box', 'cooperation_meta_box_nonce');
    
    // 获取现有值
    $create_date = get_post_meta($post->ID, 'create_date', true);
    $enter_date = get_post_meta($post->ID, 'enter_date', true);
    $person = get_post_meta($post->ID, 'person', true);
    $contact_phone = get_post_meta($post->ID, 'contact_phone', true);
    $contact_email = get_post_meta($post->ID, 'contact_email', true);
    $address = get_post_meta($post->ID, 'address', true);
    $website = get_post_meta($post->ID, 'website', true);
    $business_scope = get_post_meta($post->ID, 'business_scope', true);
    $cooperation_status = get_post_meta($post->ID, 'cooperation_status', true);
    
    ?>
    <table class="form-table">
        <tr>
            <th><label for="create_date">成立时间</label></th>
            <td><input type="date" id="create_date" name="create_date" value="<?php echo esc_attr($create_date); ?>" /></td>
        </tr>
        <tr>
            <th><label for="enter_date">入驻时间</label></th>
            <td><input type="date" id="enter_date" name="enter_date" value="<?php echo esc_attr($enter_date); ?>" /></td>
        </tr>
        <tr>
            <th><label for="person">企业人数</label></th>
            <td><input type="number" id="person" name="person" value="<?php echo esc_attr($person); ?>" min="1" max="10000" /></td>
        </tr>
        <tr>
            <th><label for="contact_phone">联系电话</label></th>
            <td><input type="text" id="contact_phone" name="contact_phone" value="<?php echo esc_attr($contact_phone); ?>" /></td>
        </tr>
        <tr>
            <th><label for="contact_email">联系邮箱</label></th>
            <td><input type="email" id="contact_email" name="contact_email" value="<?php echo esc_attr($contact_email); ?>" /></td>
        </tr>
        <tr>
            <th><label for="address">机构地址</label></th>
            <td><textarea id="address" name="address" rows="3" cols="50"><?php echo esc_textarea($address); ?></textarea></td>
        </tr>
        <tr>
            <th><label for="website">官方网站</label></th>
            <td><input type="url" id="website" name="website" value="<?php echo esc_attr($website); ?>" /></td>
        </tr>
        <tr>
            <th><label for="business_scope">经营范围</label></th>
            <td><textarea id="business_scope" name="business_scope" rows="4" cols="50"><?php echo esc_textarea($business_scope); ?></textarea></td>
        </tr>
        <tr>
            <th><label for="cooperation_status">合作状态</label></th>
            <td>
                <select id="cooperation_status" name="cooperation_status">
                    <option value="active" <?php selected($cooperation_status, 'active'); ?>>正在合作</option>
                    <option value="pending" <?php selected($cooperation_status, 'pending'); ?>>待合作</option>
                    <option value="completed" <?php selected($cooperation_status, 'completed'); ?>>合作完成</option>
                    <option value="suspended" <?php selected($cooperation_status, 'suspended'); ?>>暂停合作</option>
                </select>
            </td>
        </tr>
    </table>
    <?php
}

// 保存自定义字段
function save_cooperation_meta_box_data($post_id) {
    // 验证nonce
    if (!isset($_POST['cooperation_meta_box_nonce'])) {
        return;
    }
    
    if (!wp_verify_nonce($_POST['cooperation_meta_box_nonce'], 'cooperation_meta_box')) {
        return;
    }
    
    // 检查用户权限
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // 保存字段
    $fields = array(
        'create_date',
        'enter_date', 
        'person',
        'contact_phone',
        'contact_email',
        'address',
        'website',
        'business_scope',
        'cooperation_status'
    );
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'save_cooperation_meta_box_data');

// 注册机构类型分类法
function register_organ_type_taxonomy() {
    $labels = array(
        'name'              => '机构类型',
        'singular_name'     => '机构类型',
        'search_items'      => '搜索机构类型',
        'all_items'        => '所有机构类型',
        'parent_item'       => '父级机构类型',
        'parent_item_colon' => '父级机构类型:',
        'edit_item'         => '编辑机构类型',
        'update_item'       => '更新机构类型',
        'add_new_item'      => '添加新机构类型',
        'new_item_name'     => '新机构类型名称',
        'menu_name'         => '机构类型',
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'organ-type'),
        'public'            => true,
        'show_in_rest'      => true,
    );

    register_taxonomy('organ_type', array('cooperation'), $args);
}
add_action('init', 'register_organ_type_taxonomy');

// 注册合作机构自定义文章类型
function register_cooperation_post_type() {
    $labels = array(
        'name'                  => '合作机构',
        'singular_name'         => '合作机构',
        'menu_name'             => '合作机构',
        'name_admin_bar'        => '合作机构',
        'archives'              => '机构档案',
        'attributes'            => '机构属性',
        'parent_item_colon'     => '父级机构:',
        'all_items'             => '所有机构',
        'add_new_item'          => '添加新机构',
        'add_new'               => '添加新机构',
        'new_item'              => '新机构',
        'edit_item'             => '编辑机构',
        'update_item'           => '更新机构',
        'view_item'             => '查看机构',
        'view_items'            => '查看机构',
        'search_items'          => '搜索机构',
        'not_found'             => '未找到机构',
        'not_found_in_trash'    => '回收站中未找到机构',
        'featured_image'        => '机构Logo',
        'set_featured_image'    => '设置机构Logo',
        'remove_featured_image' => '移除机构Logo',
        'use_featured_image'    => '使用作为机构Logo',
        'insert_into_item'      => '插入到机构',
        'uploaded_to_this_item' => '上传到此机构',
        'items_list'            => '机构列表',
        'items_list_navigation' => '机构列表导航',
        'filter_items_list'     => '筛选机构列表',
    );

    $args = array(
        'label'                 => '合作机构',
        'description'           => '合作机构信息管理',
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies'            => array('organ_type'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 20,
        'menu_icon'             => 'dashicons-building',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rewrite'               => array('slug' => 'cooperation'),
    );

    register_post_type('cooperation', $args);
}
add_action('init', 'register_cooperation_post_type');

?>
