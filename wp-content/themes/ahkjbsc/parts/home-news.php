<div class="news">
    <div class="process">
        <div class="processWrap">
            <div class="actTitle">
                <div class="name">新闻中心</div>
                <div class="desc">NEWS CENTER</div>
            </div>
            <div class="top">
                <div class="imgBox">
                    <?php get_template_part('parts/home-slider'); // 顶部图片?>
                </div>
                <?php
                // Get all categories
                $categories = get_categories(array(
                    'orderby' => 'name',
                    'order'   => 'ASC',
                    'hide_empty' => true,
                ));
                ?>
                <div class="ant-tabs ant-tabs-top">
                    <div role="tablist" class="ant-tabs-nav">
                        <div class="ant-tabs-nav-wrap">
                            <div class="ant-tabs-nav-list">
                                <?php foreach ($categories as $category) : ?>
                                <div class="ant-tabs-tab">
                                    <div role="tab" class="ant-tabs-tab-btn" data-category="<?php echo esc_attr($category->term_id); ?>">
                                        <?php echo esc_html($category->name); ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
                                     style="left: 0px; width: 96px;"></div>
                            </div>
                        </div>
                        <div class="ant-tabs-extra-content">
                            <a href="<?php echo home_url('/news/');?>" class="r_more">查看更多
                                <img width="28" height="4"
                                     src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAAKCAYAAADo3z3CAAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAPKADAAQAAAABAAAACgAAAACGdoZAAAABCklEQVRIDWNgGOLg////jLoV/wWJ9QYTsQoHqzrVUob2738ZzqhV/tcgxo1D2sMqk/6zMzAyOP7/z6D09w/DMeWy/46EPD2kPXwnj/GnqCSDA9CT6xj+Mwgy/mPYqVr6PwGfpxnxSQ4VOVA+Vitj6Pr3n6EE5GYmJobWW50MtYyMjP/R/TAsPAzzFDBJpwG9OBWYxFmAYisY5RgSQKkAJg+ih5WHQR5SKf3vBkzeq4FRywf03DEmHgb/Ww2Mb0ByIDDsPAzylGbVf51fvxm2Aj0ux8jIcJeZkcH7ZhfjTbCHVUr+xwBDYzGIM2wBI8N7RmaGoDsdjAeGdClNdAQBS3CGvww71cr+xwMAhLpK/zCCOSUAAAAASUVORK5CYII="
                                     alt="">
                            </a>
                        </div>
                    </div>
                    <div class="ant-tabs-content-holder">
                        <div class="ant-tabs-content ant-tabs-content-top">
                            <?php foreach ($categories as $category) : ?>
                            <!-- Category: <?php echo esc_html($category->name); ?> tab pane -->
                            <div class="ant-tabs-tabpane" data-category="<?php echo esc_attr($category->term_id); ?>">
                                <ul>
                                    <?php
                                    // Query for posts in this category (latest 10)
                                    $category_posts = new WP_Query(array(
                                        'post_type' => 'post',
                                        'posts_per_page' => 10,
                                        'post_status' => 'publish',
                                        'cat' => $category->term_id,
                                        'orderby' => 'date',
                                        'order' => 'DESC'
                                    ));

                                    if ($category_posts->have_posts()) :
                                        while ($category_posts->have_posts()) : $category_posts->the_post();
                                    ?>
                                        <li>
                                            <a href="<?php the_permalink(); ?>" target="_blank">
                                                <?php the_title(); ?>
                                            </a>
                                            <span class="date"><?php echo get_the_date('Y-m-d'); ?></span>
                                        </li>
                                    <?php
                                        endwhile;
                                        wp_reset_postdata();
                                    else :
                                    ?>
                                        <li>该分类下暂无文章</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>