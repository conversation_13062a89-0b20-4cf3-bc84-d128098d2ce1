<?php
/**
 * Simple XML Sitemap Template
 * This is a basic sitemap for the 404 page links
 * For production, consider using a proper SEO plugin like Yoast or RankMath
 * 
 * @package ahkjbsc
 */

// Set the content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <!-- Homepage -->
    <url>
        <loc><?php echo home_url(); ?></loc>
        <lastmod><?php echo date('Y-m-d\TH:i:s+00:00'); ?></lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
    
    <!-- Pages -->
    <?php
    $pages = get_pages(array(
        'post_status' => 'publish',
        'sort_column' => 'menu_order'
    ));
    
    foreach ($pages as $page) :
    ?>
    <url>
        <loc><?php echo get_permalink($page->ID); ?></loc>
        <lastmod><?php echo date('Y-m-d\TH:i:s+00:00', strtotime($page->post_modified)); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    <?php endforeach; ?>
    
    <!-- Posts -->
    <?php
    $posts = get_posts(array(
        'numberposts' => 100,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    foreach ($posts as $post) :
    ?>
    <url>
        <loc><?php echo get_permalink($post->ID); ?></loc>
        <lastmod><?php echo date('Y-m-d\TH:i:s+00:00', strtotime($post->post_modified)); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.6</priority>
    </url>
    <?php endforeach; ?>
    
    <!-- Categories -->
    <?php
    $categories = get_categories(array(
        'hide_empty' => true
    ));
    
    foreach ($categories as $category) :
    ?>
    <url>
        <loc><?php echo get_category_link($category->term_id); ?></loc>
        <lastmod><?php echo date('Y-m-d\TH:i:s+00:00'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.5</priority>
    </url>
    <?php endforeach; ?>
</urlset>
