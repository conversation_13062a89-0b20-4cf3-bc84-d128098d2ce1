<?php
/**
 * The main template file.
 *
 * To override home page (for listing latest post) add home.php into the theme.<br>
 * If front page displays is set to static, the index.php file will be use.<br>
 * If front-page.php exists, it will be override any home page file such as home.php, index.php.<br>
 * To learn more please go to https://developer.wordpress.org/themes/basics/template-hierarchy/ .
 *
 * @package ahkjbsc-old
 */


// begins template. -------------------------------------------------------------------------
get_header();
get_sidebar();
?>
    <main id="main" class="col-md-12 site-main" role="main">
        <?php
        if (have_posts()) {
            while (have_posts()) {
                the_post();

                get_template_part('content', get_post_format());
            }// endwhile;
        } else {
            get_template_part('content', 'none');
        }// endif;
        ?>
    </main>
<?php
get_sidebar('right');
get_footer();