<?php
/**
 * Template for displaying posts in simple list format
 *
 * @package ahkjbsc
 */
?>

<div class="news-list-item">
    <div class="ant-list-item">
        <div class="ant-list-item-content">
            <div class="ant-list-item-content-single">
                <div class="ant-list-item-meta">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="ant-list-item-meta-avatar">
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('thumbnail', array('class' => 'ant-avatar ant-avatar-square ant-avatar-lg')); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                    <div class="ant-list-item-meta-content">
                        <div class="ant-list-item-meta-title">
                            <a href="<?php the_permalink(); ?>" rel="bookmark">
                                <?php the_title(); ?>
                            </a>
                        </div>
                        <div class="ant-list-item-meta-description">
                            <div class="news-meta-info">
                                <span class="news-date">
                                    <i class="anticon anticon-calendar"></i>
                                    <?php echo get_the_date('Y-m-d H:i'); ?>
                                </span>
                                <?php if (get_the_category()) : ?>
                                    <span class="news-category">
                                    <i class="anticon anticon-folder"></i>
                                    <?php
                                    $categories = get_the_category();
                                    echo esc_html($categories[0]->name);
                                    ?>
                                </span>
                                <?php endif; ?>
                                <span class="news-views">
                                    <i class="anticon anticon-eye"></i>
                                    <?php
                                    $views = get_post_meta(get_the_ID(), 'post_views', true);
                                    echo $views ? $views : '0';
                                    ?> 次浏览
                                </span>
                            </div>
                            <div class="news-excerpt">
                                <?php
                                if (has_excerpt()) {
                                    echo wp_trim_words(get_the_excerpt(), 30, '...');
                                } else {
                                    echo wp_trim_words(get_the_content(), 30, '...');
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ant-list-item-action">
                <a href="<?php the_permalink(); ?>" class="ant-btn ant-btn-link">
                    查看详情
                    <i class="anticon anticon-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>
