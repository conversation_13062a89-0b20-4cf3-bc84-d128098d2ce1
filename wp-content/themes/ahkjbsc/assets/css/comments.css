/* Comments Styles */

.comments-area {
    margin-top: 32px;
}

.comments-list-title {
    margin: 0 0 24px 0;
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
}

.comment-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.comment-item {
    margin-bottom: 24px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border-left: 4px solid #1890ff;
}

.comment-item .children {
    margin-top: 16px;
    margin-left: 32px;
    list-style: none;
    padding: 0;
}

.comment-item .children .comment-item {
    background: #fff;
    border-left-color: #d9d9d9;
}

.comment-body {
    position: relative;
}

.comment-meta {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.comment-author {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.comment-avatar {
    border-radius: 50%;
    flex-shrink: 0;
}

.comment-author-info {
    flex: 1;
}

.comment-author-name {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.comment-author-name a {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
}

.comment-author-name a:hover {
    color: #1890ff;
}

.author-badge {
    background: #1890ff;
    color: #fff;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.comment-metadata {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

.comment-metadata time {
    margin-right: 12px;
}

.edit-link a {
    color: rgba(0, 0, 0, 0.45);
    text-decoration: none;
}

.edit-link a:hover {
    color: #1890ff;
}

.comment-actions {
    display: flex;
    align-items: center;
}

.comment-reply-link {
    color: #1890ff;
    text-decoration: none;
    font-size: 12px;
    padding: 4px 8px;
    border: 1px solid #1890ff;
    border-radius: 4px;
    transition: all 0.3s;
}

.comment-reply-link:hover {
    background: #1890ff;
    color: #fff;
}

.comment-content {
    font-size: 14px;
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.85);
}

.comment-content p {
    margin: 0 0 12px 0;
}

.comment-content p:last-child {
    margin-bottom: 0;
}

.comment-awaiting-moderation {
    background: #fff7e6;
    border: 1px solid #ffd591;
    color: #d46b08;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 12px;
}

/* 评论导航 */
.comment-navigation {
    display: flex;
    justify-content: space-between;
    margin: 24px 0;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
}

.comment-navigation a {
    color: #1890ff;
    text-decoration: none;
    padding: 8px 16px;
    border: 1px solid #1890ff;
    border-radius: 4px;
    transition: all 0.3s;
}

.comment-navigation a:hover {
    background: #1890ff;
    color: #fff;
}

.no-comments {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-style: italic;
    padding: 32px;
    background: #fafafa;
    border-radius: 8px;
}

/* 评论表单 */
#commentform {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    margin-top: 32px;
}

#commentform h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
}

.ant-form-item {
    margin-bottom: 16px;
}

.ant-form-item-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
}

.ant-form-item-control {
    position: relative;
}

.ant-input {
    box-sizing: border-box;
    margin: 0;
    padding: 8px 12px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 1.5715;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s;
    width: 100%;
}

.ant-input:focus {
    border-color: #40a9ff;
    border-right-width: 1px;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-input::placeholder {
    color: rgba(0, 0, 0, 0.25);
}

textarea.ant-input {
    resize: vertical;
    min-height: 120px;
}

.ant-btn {
    line-height: 1.5715;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    user-select: none;
    touch-action: manipulation;
    height: 36px;
    padding: 6px 16px;
    font-size: 14px;
    border-radius: 6px;
}

.ant-btn-primary {
    color: #fff;
    background: #1890ff;
    border-color: #1890ff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-primary:hover {
    color: #fff;
    background: #40a9ff;
    border-color: #40a9ff;
}

.comment-notes {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 16px;
    padding: 8px 12px;
    background: #f6f8fa;
    border-radius: 4px;
}

/* 回复表单 */
#respond {
    margin-top: 16px;
}

#respond h3 {
    font-size: 16px;
    margin-bottom: 16px;
}

#cancel-comment-reply-link {
    color: #ff4d4f;
    text-decoration: none;
    font-size: 12px;
    margin-left: 8px;
}

#cancel-comment-reply-link:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .comment-item {
        padding: 16px;
    }
    
    .comment-item .children {
        margin-left: 16px;
    }
    
    .comment-meta {
        flex-direction: column;
        gap: 12px;
    }
    
    .comment-actions {
        align-self: flex-start;
    }
    
    #commentform {
        padding: 16px;
    }
    
    .ant-row {
        flex-direction: column;
    }
    
    .ant-col {
        width: 100% !important;
        margin-bottom: 16px;
    }
    
    .ant-col:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 480px) {
    .comment-author {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 8px;
    }
    
    .comment-navigation {
        flex-direction: column;
        gap: 12px;
    }
    
    .comment-navigation a {
        text-align: center;
    }
}
