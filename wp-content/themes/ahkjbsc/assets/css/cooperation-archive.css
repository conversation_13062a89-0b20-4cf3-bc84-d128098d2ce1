/* Cooperation Archive Page Styles */

.cooperation-archive {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 40px 0;
}

/* é¡µé¢å¤´éƒ¨ */
.page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 12px 0;
}

.page-description {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.65);
    margin: 0;
}

/* ç­›é€‰å™¨åŒºåŸŸ */
.cooperation-filters {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-group {
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20px;
    display: flex;
    align-items: center;
}

.filter-group:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.filter-label {
    font-size: 14px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    padding: 8px 16px;
    background: #f0f8ff;
    border-radius: 20px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
    margin-right: 10px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.filter-options-extended {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px dashed #d9d9d9;
}

.filter-btn {
    padding: 6px 16px;
    border: 1px solid #d9d9d9;
    background: #fff;
    color: rgba(0, 0, 0, 0.65);
    border-radius: 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
    line-height: 1.4;
}

.filter-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
    background: #f0f8ff;
}

.filter-btn.active {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
    font-weight: 500;
}

.filter-btn .count {
    margin-left: 4px;
    font-size: 11px;
    opacity: 0.8;
}

.filter-btn.active .count {
    opacity: 1;
}

.more-btn {
    background: #fafafa;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

.more-btn:hover {
    background: #f0f0f0;
    border-color: #bfbfbf;
    color: rgba(0, 0, 0, 0.65);
}

/* æŽ§åˆ¶åŒºåŸŸ */
.cooperation-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 20px 24px;
    border-radius: 12px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    flex-wrap: wrap;
    gap: 16px;
}

.search-section {
    flex: 1;
    max-width: 400px;
}

.ant-input-search {
    display: flex;
    width: 100%;
}

.ant-input-search .ant-input {
    flex: 1;
    height: 36px;
    border-radius: 6px 0 0 6px;
    border-right: none;
}

.ant-input-search .ant-input-group-addon {
    border-radius: 0 6px 6px 0;
    border-left: none;
}

.ant-input-search .ant-btn {
    height: 36px;
    border-radius: 0 6px 6px 0;
    border: none;
}

.sort-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ant-select {
    min-width: 120px;
    height: 36px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 0 12px;
    background: #fff;
    color: rgba(0, 0, 0, 0.85);
}

.results-count {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    white-space: nowrap;
}

.results-count strong {
    color: #1890ff;
    font-weight: 600;
}

/* æœºæž„ç½‘æ ¼ */
.cooperation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

/* æœºæž„å¡ç‰‡ */
.cooperation-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s;
    overflow: hidden;
    border: 1px solid #f0f0f0;
}

.cooperation-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
    border-color: #1890ff;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.card-image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #fafafa;
    border: 1px solid #f0f0f0;
}

.cooperation-logo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-info {
    flex: 1;
    min-width: 0;
}

.cooperation-name {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.cooperation-name a {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
}

.cooperation-name a:hover {
    color: #1890ff;
}

.cooperation-type {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.type-tag {
    padding: 2px 8px;
    background: #f0f8ff;
    color: #1890ff;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid #d6e4ff;
}

.card-body {
    padding: 20px;
}

.cooperation-excerpt {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cooperation-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.meta-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
}

.meta-item .anticon {
    margin-right: 6px;
    color: #1890ff;
    font-size: 14px;
}

.meta-label {
    margin-right: 4px;
    color: rgba(0, 0, 0, 0.45);
}

.meta-value {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
}

.card-footer {
    padding: 16px 20px;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #f0f0f0;
}

.card-footer .ant-btn {
    height: 32px;
    padding: 4px 16px;
    font-size: 13px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.card-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d9d9d9;
    background: #fff;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    color: rgba(0, 0, 0, 0.45);
}

.action-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
    background: #f0f8ff;
}

.action-btn.favorited {
    color: #ff4d4f;
    border-color: #ff4d4f;
    background: #fff2f0;
}

/* 分页 */
.cooperation-pagination {
    margin-top: 40px;
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.ant-pagination-item {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    padding: 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: all 0.3s;
    font-size: 14px;
    cursor: pointer;
}

.ant-pagination-item:hover:not([disabled]):not(.ant-pagination-item-ellipsis) {
    border-color: #1890ff;
    color: #1890ff;
    transform: translateY(-1px);
}

.ant-pagination-item-active {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
    font-weight: 600;
}

.ant-pagination-item-active:hover {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
}

.ant-pagination-item[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

.ant-pagination-item-ellipsis {
    border: none;
    background: none;
    cursor: default;
    color: rgba(0, 0, 0, 0.25);
}

.ant-pagination-prev,
.ant-pagination-next {
    padding: 0 12px;
    font-size: 13px;
}

/* 分页信息 */
.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
}

.pagination-summary {
    font-weight: 500;
}

.pagination-jump {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-jump input {
    width: 60px;
    height: 28px;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    text-align: center;
    font-size: 13px;
}

.pagination-jump input:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.pagination-jump .ant-btn {
    height: 28px;
    padding: 4px 12px;
    font-size: 12px;
    border-radius: 4px;
}

/* ç©ºçŠ¶æ€ */
.no-results {
    text-align: center;
    padding: 60px 20px;
}

.ant-empty {
    color: rgba(0, 0, 0, 0.25);
}

.ant-empty-image {
    margin-bottom: 16px;
}

.ant-empty-description {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.45);
}

/* å“åº”å¼è®¾è®¡ */
@media (max-width: 1200px) {
    .cooperation-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .cooperation-archive {
        padding: 20px 0;
    }

    .page-header {
        margin-bottom: 20px;
        padding: 20px;
    }

    .page-title {
        font-size: 24px;
    }

    .cooperation-filters {
        padding: 16px;
        margin-bottom: 16px;
    }

    .filter-group {
        margin-bottom: 16px;
        padding-bottom: 16px;
    }

    .filter-label {
        display: block;
        margin-bottom: 8px;
        text-align: left;
        padding: 6px 12px;
        font-size: 13px;
    }

    .filter-options {
        gap: 6px;
    }

    .filter-btn {
        padding: 4px 12px;
        font-size: 12px;
    }

    .cooperation-controls {
        flex-direction: column;
        align-items: stretch;
        padding: 16px;
        gap: 12px;
    }

    .search-section {
        max-width: none;
    }

    .sort-section {
        justify-content: space-between;
    }

    .results-count {
        text-align: center;
    }

    .cooperation-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 20px;
    }

    .cooperation-card {
        margin: 0 16px;
    }

    .card-header {
        padding: 16px;
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 12px;
    }

    .card-image {
        width: 80px;
        height: 80px;
    }

    .card-body {
        padding: 16px;
    }

    .cooperation-meta {
        gap: 6px;
    }

    .card-footer {
        padding: 12px 16px;
        flex-direction: column;
        gap: 12px;
    }

    .card-footer .ant-btn {
        width: 100%;
        justify-content: center;
    }

    .card-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .cooperation-archive {
        padding: 16px 0;
    }

    .page-header {
        padding: 16px;
        margin: 0 16px 16px 16px;
    }

    .page-title {
        font-size: 20px;
    }

    .cooperation-filters {
        margin: 0 16px 16px 16px;
        padding: 12px;
    }

    .cooperation-controls {
        margin: 0 16px 16px 16px;
        padding: 12px;
    }

    .filter-btn {
        padding: 3px 8px;
        font-size: 11px;
    }

    .cooperation-card {
        margin: 0 12px;
    }

    .card-header {
        padding: 12px;
    }

    .card-body {
        padding: 12px;
    }

    .card-footer {
        padding: 10px 12px;
    }
}

/* åŠ è½½åŠ¨ç”» */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cooperation-card {
    animation: fadeInUp 0.3s ease-out;
}

/* ç­›é€‰å™¨åŠ¨ç”» */
.filter-btn {
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(24, 144, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.filter-btn:hover::before {
    width: 100%;
    height: 100%;
}

/* å¡ç‰‡æ‚¬åœæ•ˆæžœå¢žå¼º */
.cooperation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(24, 144, 255, 0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.cooperation-card:hover::before {
    opacity: 1;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    border-radius: 8px;
}

.loading-spinner {
    text-align: center;
}

.loading-text {
    margin-top: 12px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
}

.ant-spin {
    display: inline-block;
    color: #1890ff;
}

.ant-spin-dot {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    transform: rotate(45deg);
    animation: antRotate 1.2s infinite linear;
}

.ant-spin-dot-item {
    position: absolute;
    display: block;
    width: 9px;
    height: 9px;
    background-color: #1890ff;
    border-radius: 100%;
    transform: scale(0.75);
    transform-origin: 50% 50%;
    opacity: 0.3;
    animation: antSpinMove 1s infinite linear alternate;
}

.ant-spin-dot-item:nth-child(1) {
    top: 0;
    left: 0;
}

.ant-spin-dot-item:nth-child(2) {
    top: 0;
    right: 0;
    animation-delay: 0.4s;
}

.ant-spin-dot-item:nth-child(3) {
    right: 0;
    bottom: 0;
    animation-delay: 0.8s;
}

.ant-spin-dot-item:nth-child(4) {
    bottom: 0;
    left: 0;
    animation-delay: 1.2s;
}

@keyframes antRotate {
    to {
        transform: rotate(405deg);
    }
}

@keyframes antSpinMove {
    to {
        opacity: 1;
    }
}

/* 列表加载状态 */
.cooperation-list {
    position: relative;
}

.cooperation-grid.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 按钮加载状态 */
.ant-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

.ant-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 14px;
    height: 14px;
    margin: -7px 0 0 -7px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
