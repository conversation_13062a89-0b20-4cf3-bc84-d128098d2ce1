/* Print Styles */
@media print {
    /* 隐藏不需要打印的元素 */
    .article-breadcrumb,
    .article-actions,
    .article-tags,
    .article-navigation,
    .article-comments,
    .article-sidebar,
    .ant-breadcrumb,
    .action-buttons,
    .comment-reply-link,
    #commentform,
    .nav-previous,
    .nav-next {
        display: none !important;
    }
    
    /* 重置页面样式 */
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.5;
        font-family: "Times New Roman", serif;
    }
    
    .page-article-detail {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .ant-row {
        display: block !important;
    }
    
    .ant-col {
        width: 100% !important;
        float: none !important;
        display: block !important;
    }
    
    .article-main {
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .article-title {
        font-size: 18pt !important;
        font-weight: bold !important;
        margin-bottom: 12pt !important;
        page-break-after: avoid;
    }
    
    .article-meta-info {
        font-size: 10pt !important;
        margin-bottom: 12pt !important;
        border-bottom: 1px solid #ccc !important;
        padding-bottom: 6pt !important;
    }
    
    .article-content {
        font-size: 12pt !important;
        line-height: 1.6 !important;
    }
    
    .article-body h1,
    .article-body h2,
    .article-body h3,
    .article-body h4,
    .article-body h5,
    .article-body h6 {
        page-break-after: avoid;
        margin-top: 12pt !important;
        margin-bottom: 6pt !important;
    }
    
    .article-body h2 {
        font-size: 16pt !important;
    }
    
    .article-body h3 {
        font-size: 14pt !important;
    }
    
    .article-body p {
        margin-bottom: 6pt !important;
        orphans: 3;
        widows: 3;
    }
    
    .article-body img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
    }
    
    .article-featured-image {
        text-align: center !important;
        margin-bottom: 12pt !important;
    }
    
    /* 分页控制 */
    .article-header {
        page-break-after: avoid;
    }
    
    .article-content {
        page-break-before: avoid;
    }
    
    /* 链接处理 */
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }
    
    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: "";
    }
    
    /* 表格样式 */
    table {
        border-collapse: collapse !important;
        width: 100% !important;
    }
    
    table, th, td {
        border: 1px solid #000 !important;
    }
    
    th, td {
        padding: 4pt !important;
        text-align: left !important;
    }
    
    th {
        font-weight: bold !important;
        background: #f0f0f0 !important;
    }
}
