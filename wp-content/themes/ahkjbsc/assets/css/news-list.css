/* News List Styles */
.news-list-item {
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20px;
}

.news-list-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.ant-list-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
}

.ant-list-item-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.ant-list-item-content-single {
    flex: 1;
}

.ant-list-item-meta {
    display: flex;
    align-items: flex-start;
}

.ant-list-item-meta-avatar {
    margin-right: 16px;
    flex-shrink: 0;
}

.ant-avatar {
    display: inline-block;
    text-align: center;
    color: #fff;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    vertical-align: middle;
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
}

.ant-avatar-lg {
    width: 80px;
    height: 80px;
    line-height: 80px;
    border-radius: 8px;
}

.ant-avatar-square {
    border-radius: 6px;
}

.ant-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ant-list-item-meta-content {
    flex: 1;
}

.ant-list-item-meta-title {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #383b40;
    line-height: 27px;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.ant-list-item-meta-title a {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
}

.ant-list-item-meta-title a:hover {
    color: #1890ff;
}

.ant-list-item-meta-description {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
}

.news-meta-info {
    margin-bottom: 8px;
}

.news-meta-info span {
    margin-right: 16px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

.news-meta-info .anticon {
    margin-right: 4px;
}

.news-excerpt {
    color: rgba(0, 0, 0, 0.65);
    line-height: 1.5;
}

.ant-list-item-action {
    margin-left: 16px;
    flex-shrink: 0;
}

.ant-btn {
    line-height: 1.5715;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    user-select: none;
    touch-action: manipulation;
    height: 32px;
    padding: 4px 15px;
    font-size: 14px;
    border-radius: 6px;
    text-decoration: none;
}

.ant-btn-link {
    color: #1890ff;
    background: transparent;
    border-color: transparent;
    box-shadow: none;
}

.ant-btn-link:hover {
    color: #40a9ff;
    background: transparent;
    border-color: transparent;
}

.ant-btn .anticon {
    margin-left: 4px;
}

/* Search and Filter Styles */
.news-filters {
    background: #fafafa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
}

.ant-input-group {
    position: relative;
    display: table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.ant-input {
    box-sizing: border-box;
    margin: 0;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 1.5715;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s;
    display: table-cell;
    position: relative;
    width: 100%;
}

.ant-input:focus {
    border-color: #40a9ff;
    border-right-width: 1px;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-input-group-addon {
    position: relative;
    padding: 0 11px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: normal;
    font-size: 14px;
    text-align: center;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s;
    display: table-cell;
    width: 1px;
    white-space: nowrap;
    vertical-align: middle;
}

.ant-input-search .ant-input-group-addon {
    background-color: transparent;
    border: none;
    padding: 0;
}


.ant-btn-primary {
    color: #fff;
    background: #1890ff;
    border-color: #1890ff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-primary:hover {
    color: #fff;
    background: #40a9ff;
    border-color: #40a9ff;
}

.ant-select {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.ant-select-selection {
    width: 100%;
    height: 32px;
    padding: 4px 11px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    font-size: 14px;
    line-height: 1.5715;
    color: rgba(0, 0, 0, 0.85);
}

.ant-select-selection:focus {
    border-color: #40a9ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Empty State */
.ant-empty {
    margin: 0 8px;
    font-size: 14px;
    line-height: 1.5715;
    text-align: center;
}

.ant-empty-image {
    height: 100px;
    margin-bottom: 8px;
}

.ant-empty-description {
    color: rgba(0, 0, 0, 0.25);
}

/* Pagination */
.ant-pagination {
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
    display: flex;
    align-items: center;
    justify-content: center;
}

.ant-pagination-item {
    display: inline-block;
    min-width: 32px;
    height: 32px;
    margin-right: 8px;
    font-family: Arial;
    line-height: 30px;
    text-align: center;
    vertical-align: middle;
    list-style: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    outline: 0;
    cursor: pointer;
    user-select: none;
    text-decoration: none;
    color: rgba(0, 0, 0, 0.85);
}
.ant-pagination-item.next,.ant-pagination-item.prev {
    padding: 0 3px
}

.ant-pagination-item:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.ant-pagination-item-active {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
}

.ant-pagination-item-active:hover {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
}

/* Responsive */
@media (max-width: 768px) {
    .news-filters .ant-row {
        flex-direction: column;
    }

    .news-filters .ant-col {
        width: 100% !important;
        margin-bottom: 16px;
    }

    .news-filters .ant-col:last-child {
        margin-bottom: 0;
        text-align: left !important;
    }

    .ant-list-item-meta {
        flex-direction: column;
    }

    .ant-list-item-meta-avatar {
        margin-right: 0;
        margin-bottom: 12px;
        align-self: center;
    }

    .ant-list-item-action {
        margin-left: 0;
        margin-top: 12px;
    }
}
