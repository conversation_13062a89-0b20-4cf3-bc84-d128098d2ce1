/* Article Detail Page Styles */

/* 面包屑导航 */
.article-breadcrumb {
    background: #fafafa;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
}

.ant-breadcrumb {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 1.5715;
}

.ant-breadcrumb-link a {
    color: rgba(0, 0, 0, 0.45);
    text-decoration: none;
    transition: color 0.3s;
}

.ant-breadcrumb-link a:hover {
    color: #1890ff;
}

.ant-breadcrumb-separator {
    margin: 0 8px;
    color: rgba(0, 0, 0, 0.45);
}

/* 文章内容区域 */
.article-content-wrapper {
    padding: 40px 0;
}

.article-main {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 32px;
    margin-bottom: 24px;
}

/* 文章头部 */
.article-header {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 24px;
    margin-bottom: 32px;
}

.article-title {
    font-size: 28px;
    font-weight: 600;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 20px 0;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.article-meta-info {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.meta-item {
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
}

.meta-item .anticon {
    margin-right: 6px;
    color: rgba(0, 0, 0, 0.45);
}

.meta-label {
    margin-right: 4px;
    font-weight: 500;
}

.category-link {
    color: #1890ff;
    text-decoration: none;
}

.category-link:hover {
    color: #40a9ff;
}

/* 操作按钮 */
.article-actions {
    display: flex;
    align-items: center;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.action-buttons .ant-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    border: 1px solid #d9d9d9;
    background: #fff;
    color: rgba(0, 0, 0, 0.65);
    transition: all 0.3s;
}

.action-buttons .ant-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
}

/* 文章内容 */
.article-content {
    margin-bottom: 32px;
}

.article-featured-image {
    margin-bottom: 24px;
    text-align: center;
}

.article-featured-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.article-body {
    font-size: 16px;
    line-height: 1.8;
    color: rgba(0, 0, 0, 0.85);
}

.article-body p {
    margin-bottom: 16px;
}

.article-body img {
    max-width: 100%;
    height: auto;
    margin: 16px 0;
    border-radius: 6px;
    transition: transform 0.3s;
}

.article-body img:hover {
    transform: scale(1.02);
}

.article-body h1,
.article-body h2,
.article-body h3,
.article-body h4,
.article-body h5,
.article-body h6 {
    margin: 24px 0 16px 0;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
}

.article-body h2 {
    font-size: 24px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
}

.article-body h3 {
    font-size: 20px;
}

.article-body h4 {
    font-size: 18px;
}

.article-body blockquote {
    margin: 16px 0;
    padding: 16px 20px;
    background: #f6f8fa;
    border-left: 4px solid #1890ff;
    border-radius: 0 6px 6px 0;
}

.article-body ul,
.article-body ol {
    margin: 16px 0;
    padding-left: 24px;
}

.article-body li {
    margin-bottom: 8px;
}

.article-body code {
    background: #f6f8fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
}

.article-body pre {
    background: #f6f8fa;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 16px 0;
}

.article-body pre code {
    background: none;
    padding: 0;
}

/* 分页 */
.article-pagination {
    margin: 24px 0;
    text-align: center;
}

/* 标签 */
.article-tags {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 32px;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
}

.tags-label {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 500;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.ant-tag {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.65);
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s;
}

.ant-tag:hover {
    color: #1890ff;
    border-color: #1890ff;
    background: #f0f8ff;
}

/* 文章导航 */
.article-navigation {
    border-top: 1px solid #f0f0f0;
    padding-top: 24px;
    margin-bottom: 32px;
}

.nav-previous,
.nav-next {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.nav-previous:hover,
.nav-next:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.nav-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: rgba(0, 0, 0, 0.85);
}

.nav-content {
    flex: 1;
}

.nav-label {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 4px;
}

.nav-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.nav-previous .nav-content {
    margin-left: 12px;
}

.nav-next .nav-content {
    margin-right: 12px;
    text-align: right;
}

/* 评论区域 */
.article-comments {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 32px;
}

.comments-title h3 {
    margin: 0 0 24px 0;
    font-size: 20px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
}

/* 侧边栏 */
.article-sidebar {
    padding-left: 24px;
}

.sidebar-widget {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 24px;
    margin-bottom: 24px;
}

.widget-title {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
}

/* 相关文章 */
.related-post-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.related-post-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.related-post-item .ant-list-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.related-post-thumb {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    overflow: hidden;
    border-radius: 6px;
}

.related-post-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-post-content {
    flex: 1;
}

.related-post-title {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.related-post-title a {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
}

.related-post-title a:hover {
    color: #1890ff;
}

.related-post-meta {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    text-align: right;
}

/* 热门文章 */
.popular-post-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.popular-post-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.popular-post-rank {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    background: #1890ff;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

.popular-post-content {
    flex: 1;
}

.popular-post-title {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.popular-post-title a {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
}

.popular-post-title a:hover {
    color: #1890ff;
}

.popular-post-meta {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

/* 最新文章 */
.latest-post-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.latest-post-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.latest-post-title {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.latest-post-title a {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
}

.latest-post-title a:hover {
    color: #1890ff;
}

.latest-post-meta {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    text-align: right;
}

/* 图片模态框 */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.image-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.image-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    cursor: default;
}

.image-modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.image-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s;
}

.image-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 消息提示 */
.ant-message-notice {
    position: fixed;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1010;
    animation: slideInDown 0.3s ease-out;
}

.ant-message-notice-content {
    padding: 8px 16px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-message-custom-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ant-message-success {
    color: #52c41a;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .article-content-wrapper {
        padding: 20px 0;
    }

    .article-main {
        padding: 20px;
        margin: 0 16px 16px 16px;
    }

    .article-title {
        font-size: 22px;
    }

    .article-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .article-meta-info {
        flex-direction: column;
        gap: 8px;
    }

    .article-sidebar {
        padding-left: 0;
        margin-top: 24px;
    }

    .sidebar-widget {
        margin: 0 16px 16px 16px;
    }

    .article-navigation .ant-row {
        flex-direction: column;
        gap: 16px;
    }

    .nav-next .nav-content {
        text-align: left;
    }

    .action-buttons {
        flex-wrap: wrap;
    }

    .article-comments {
        margin: 0 16px;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .article-main {
        padding: 16px;
    }

    .article-title {
        font-size: 20px;
    }

    .article-body {
        font-size: 15px;
    }

    .sidebar-widget {
        padding: 16px;
    }
}
