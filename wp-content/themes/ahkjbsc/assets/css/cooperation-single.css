/* Single Cooperation Page Styles */

.cooperation-single {
    background: #f5f5f5;
    min-height: 100vh;
}

/* 面包屑导航 */
.cooperation-breadcrumb {
    background: #fff;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
}

/* 内容区域 */
.cooperation-content-wrapper {
    padding: 40px 0;
}

.cooperation-main {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* 机构头部 */
.cooperation-header {
    padding: 32px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f0f8ff 0%, #fff 100%);
}

.cooperation-basic-info {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    margin-bottom: 24px;
}

.cooperation-logo {
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    background: #fff;
    border: 2px solid #f0f0f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cooperation-info {
    flex: 1;
    min-width: 0;
}

.cooperation-title {
    font-size: 28px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 16px 0;
    line-height: 1.3;
}

.cooperation-types {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.type-badge {
    padding: 6px 12px;
    background: #1890ff;
    color: #fff;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.cooperation-meta-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.meta-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
}

.meta-item .anticon {
    margin-right: 8px;
    color: #1890ff;
    font-size: 16px;
}

.meta-label {
    margin-right: 6px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 500;
}

.meta-value {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
}

.cooperation-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.cooperation-actions .ant-btn {
    height: 40px;
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s;
}

.cooperation-actions .ant-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 内容区域 */
.cooperation-content {
    padding: 32px;
}

.content-section {
    margin-bottom: 32px;
}

.content-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 12px;
    border-bottom: 2px solid #1890ff;
}

.section-title .anticon {
    color: #1890ff;
    font-size: 18px;
}

.section-content {
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.8;
    font-size: 15px;
}

.section-content p {
    margin-bottom: 16px;
}

.section-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 16px 0;
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.info-item {
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    border-left: 4px solid #1890ff;
}

.info-label {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 4px;
    font-size: 13px;
}

.info-value {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
}

/* 侧边栏 */
.cooperation-sidebar {
    padding-left: 24px;
}

.sidebar-widget {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
}

.widget-title {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
}

/* 联系信息 */
.contact-info {
    margin-bottom: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.contact-item .anticon {
    color: #1890ff;
    font-size: 14px;
    width: 16px;
}

.contact-item span:first-of-type {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    min-width: 60px;
}

.contact-item a {
    color: #1890ff;
    text-decoration: none;
    font-size: 13px;
}

.contact-item a:hover {
    text-decoration: underline;
}

.ant-btn-block {
    width: 100%;
    justify-content: center;
}

/* 相关机构 */
.related-cooperations {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.related-cooperation-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 16px;
}

.related-cooperation-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.related-cooperation-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.related-cooperation-thumb {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
    background: #fafafa;
}

.related-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-cooperation-info {
    flex: 1;
    min-width: 0;
}

.related-cooperation-title {
    margin: 0 0 6px 0;
    font-size: 13px;
    line-height: 1.4;
}

.related-cooperation-title a {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
}

.related-cooperation-title a:hover {
    color: #1890ff;
}

.related-cooperation-meta {
    font-size: 11px;
    color: rgba(0, 0, 0, 0.45);
}

.no-related {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-style: italic;
    margin: 0;
}

/* 统计信息 */
.stats-info {
    display: flex;
    justify-content: space-between;
    text-align: center;
}

.stat-item {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

/* 收藏状态 */
.ant-btn.favorited {
    background: #fff2f0;
    border-color: #ff4d4f;
    color: #ff4d4f;
}

.ant-btn.favorited:hover {
    background: #fff2f0;
    border-color: #ff4d4f;
    color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cooperation-content-wrapper {
        padding: 20px 0;
    }
    
    .cooperation-header {
        padding: 20px;
    }
    
    .cooperation-basic-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 16px;
    }
    
    .cooperation-logo {
        width: 100px;
        height: 100px;
    }
    
    .cooperation-title {
        font-size: 22px;
    }
    
    .cooperation-actions {
        justify-content: center;
        width: 100%;
    }
    
    .cooperation-actions .ant-btn {
        flex: 1;
        min-width: 0;
        justify-content: center;
    }
    
    .cooperation-content {
        padding: 20px;
    }
    
    .section-title {
        font-size: 18px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .cooperation-sidebar {
        padding-left: 0;
        margin-top: 20px;
    }
    
    .sidebar-widget {
        margin: 0 20px 20px 20px;
        padding: 16px;
    }
    
    .stats-info {
        flex-direction: column;
        gap: 16px;
    }
    
    .stat-item {
        padding: 12px;
        background: #fafafa;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .cooperation-header {
        padding: 16px;
    }
    
    .cooperation-title {
        font-size: 20px;
    }
    
    .cooperation-actions {
        flex-direction: column;
    }
    
    .cooperation-actions .ant-btn {
        width: 100%;
    }
    
    .cooperation-content {
        padding: 16px;
    }
    
    .section-title {
        font-size: 16px;
    }
    
    .sidebar-widget {
        margin: 0 16px 16px 16px;
        padding: 12px;
    }
    
    .contact-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .related-cooperation-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .related-cooperation-thumb {
        width: 60px;
        height: 60px;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cooperation-main > * {
    animation: slideInUp 0.6s ease-out;
}

.sidebar-widget {
    animation: slideInUp 0.6s ease-out;
}

/* 悬停效果 */
.sidebar-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.related-cooperation-item:hover {
    background: #f0f8ff;
    border-radius: 6px;
    padding: 8px;
    margin: -8px;
    transition: all 0.3s ease;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
