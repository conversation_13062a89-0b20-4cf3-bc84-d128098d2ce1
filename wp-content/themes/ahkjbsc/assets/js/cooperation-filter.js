/**
 * Cooperation Archive Dynamic Filtering with AJAX Pagination
 *
 * @package ahkjbsc
 */

(function($) {
    'use strict';

    let currentPage = 1;
    let isLoading = false;

    // 初始化筛选功能
    function initCooperationFilters() {
        // 筛选按钮点击事件
        $(document).on('click', '.filter-btn:not(.more-btn)', function(e) {
            e.preventDefault();

            if (isLoading) return;

            const $this = $(this);
            const filterType = $this.data('filter');
            const filterValue = $this.data('value');

            // 更新按钮状态
            $this.siblings('.filter-btn:not(.more-btn)').removeClass('active');
            $this.addClass('active');

            // 重置到第一页
            currentPage = 1;

            // 执行筛选
            applyFilters();
        });

        // 更多按钮功能
        $(document).on('click', '.more-btn', function(e) {
            e.preventDefault();

            const toggleTarget = $(this).data('toggle');
            const $extendedOptions = $('#' + toggleTarget + '-extended');

            if ($extendedOptions.length) {
                if ($extendedOptions.is(':hidden')) {
                    $extendedOptions.slideDown(300);
                    $(this).html('收起 ▲');
                } else {
                    $extendedOptions.slideUp(300);
                    $(this).html('更多 ▼');
                }
            }
        });

        // 搜索框回车事件
        $(document).on('keypress', '#cooperation-search', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                currentPage = 1;
                applyFilters();
            }
        });

        // 排序选择事件
        $(document).on('change', '#cooperation-sort', function() {
            currentPage = 1;
            applyFilters();
        });

        // 分页按钮点击事件
        $(document).on('click', '.ant-pagination-item:not(.ant-pagination-item-ellipsis):not([disabled])', function(e) {
            e.preventDefault();

            if (isLoading) return;

            const page = parseInt($(this).data('page'));
            if (page && !isNaN(page)) {
                currentPage = page;
                applyFilters();
            }
        });

        // 快速跳转事件
        $(document).on('keypress', '#jump-page', function(e) {
            if (e.which === 13) {
                jumpToPage();
            }
        });

        // 初始化页面状态
        initPageState();
    }

    // 应用筛选
    function applyFilters() {
        if (isLoading) return;

        const filters = collectFilters();
        filters.paged = currentPage;

        // 显示加载状态
        showLoading();

        // 发送AJAX请求
        $.ajax({
            url: cooperation_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'filter_cooperations',
                nonce: cooperation_ajax.nonce,
                filters: filters
            },
            success: function(response) {
                if (response.success) {
                    updateCooperationList(response.data);
                    updatePagination(response.data);
                    updateResultsCount(response.data.total);
                    updateURL(filters);

                    // 滚动到列表顶部
                    $('html, body').animate({
                        scrollTop: $('.cooperation-list').offset().top - 100
                    }, 300);
                } else {
                    showError('筛选失败，请重试');
                }
            },
            error: function() {
                showError('网络错误，请重试');
            },
            complete: function() {
                hideLoading();
            }
        });
    }

    // 收集筛选条件
    function collectFilters() {
        const filters = {};

        // 收集所有激活的筛选条件
        $('.filter-btn.active').each(function() {
            const filterType = $(this).data('filter');
            const filterValue = $(this).data('value');

            if (filterValue && filterType) {
                filters[filterType] = filterValue;
            }
        });

        // 添加搜索关键词
        const searchValue = $('#cooperation-search').val().trim();
        if (searchValue) {
            filters.search = searchValue;
        }

        // 添加排序
        const sortValue = $('#cooperation-sort').val();
        if (sortValue) {
            filters.sort = sortValue;
        }

        return filters;
    }

    // 更新机构列表
    function updateCooperationList(data) {
        const $cooperationGrid = $('.cooperation-grid');

        if (data.html) {
            $cooperationGrid.fadeOut(200, function() {
                $(this).html(data.html).fadeIn(200);

                // 重新初始化卡片动画
                initCardAnimations();

                // 重新绑定卡片事件
                bindCardEvents();
            });
        } else {
            $cooperationGrid.fadeOut(200, function() {
                $(this).html('<div class="no-results"><div class="ant-empty"><p class="ant-empty-description">暂无符合条件的合作机构</p></div></div>').fadeIn(200);
            });
        }
    }

    // 更新分页
    function updatePagination(data) {
        const $paginationContainer = $('#cooperation-pagination');

        if (data.pagination) {
            $paginationContainer.html(data.pagination).show();
        } else {
            $paginationContainer.hide();
        }
    }

    // 更新结果数量
    function updateResultsCount(total) {
        $('#results-total').html('共找到 <strong>' + total + '</strong> 家合作机构');
    }

    // 更新URL
    function updateURL(filters) {
        const urlParams = new URLSearchParams();

        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                urlParams.set(key, filters[key]);
            }
        });

        const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');

        // 使用 pushState 更新URL而不刷新页面
        if (window.history && window.history.pushState) {
            window.history.pushState({}, '', newUrl);
        }
    }

    // 显示加载状态
    function showLoading() {
        $('#cooperation-list').addClass('loading');
        $('.cooperation-grid').css('opacity', '0.6');
    }

    // 隐藏加载状态
    function hideLoading() {
        $('#cooperation-list').removeClass('loading');
        $('.cooperation-grid').css('opacity', '1');
    }

    // 显示错误信息
    function showError(message) {
        // 创建错误提示
        const $error = $('<div class="cooperation-error">' + message + '</div>');
        $error.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#ff4d4f',
            color: '#fff',
            padding: '12px 20px',
            borderRadius: '6px',
            zIndex: 9999,
            boxShadow: '0 4px 12px rgba(255, 77, 79, 0.3)'
        });

        $('body').append($error);

        // 3秒后自动移除
        setTimeout(function() {
            $error.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    // 初始化卡片动画
    function initCardAnimations() {
        $('.cooperation-card').each(function(index) {
            $(this).css({
                opacity: '0',
                transform: 'translateY(20px)'
            }).delay(index * 50).animate({
                opacity: 1
            }, 300).css('transform', 'translateY(0)');
        });
    }

    // 收藏功能
    function initFavoriteFunction() {
        $(document).on('click', '.action-btn[title="收藏"]', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $icon = $btn.find('i');
            const postId = $btn.closest('.cooperation-card').data('post-id');

            // 切换收藏状态
            if ($btn.hasClass('favorited')) {
                $btn.removeClass('favorited');
                $icon.removeClass('favorited');
                $btn.css('color', '');
            } else {
                $btn.addClass('favorited');
                $icon.addClass('favorited');
                $btn.css('color', '#ff4d4f');
            }

            // 这里可以添加AJAX请求保存收藏状态到数据库
            // saveFavoriteStatus(postId, $btn.hasClass('favorited'));
        });
    }

    // 分享功能
    function initShareFunction() {
        $(document).on('click', '.action-btn[title="分享"]', function(e) {
            e.preventDefault();

            const $card = $(this).closest('.cooperation-card');
            const title = $card.find('.cooperation-name a').text();
            const url = $card.find('.cooperation-name a').attr('href');

            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                });
            } else {
                // 复制链接到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url).then(function() {
                        showSuccess('链接已复制到剪贴板');
                    });
                } else {
                    // 备用方法
                    const textArea = document.createElement('textarea');
                    textArea.value = url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showSuccess('链接已复制到剪贴板');
                }
            }
        });
    }

    // 显示成功信息
    function showSuccess(message) {
        const $success = $('<div class="cooperation-success">' + message + '</div>');
        $success.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#52c41a',
            color: '#fff',
            padding: '12px 20px',
            borderRadius: '6px',
            zIndex: 9999,
            boxShadow: '0 4px 12px rgba(82, 196, 26, 0.3)'
        });

        $('body').append($success);

        setTimeout(function() {
            $success.fadeOut(300, function() {
                $(this).remove();
            });
        }, 2000);
    }

    // 跳转到指定页面
    function jumpToPage() {
        const $jumpInput = $('#jump-page');
        if (!$jumpInput.length) return;

        const page = parseInt($jumpInput.val());
        const maxPage = parseInt($jumpInput.attr('max'));

        if (page && !isNaN(page) && page >= 1 && page <= maxPage) {
            currentPage = page;
            applyFilters();
        } else {
            $jumpInput.val(currentPage);
            showError('请输入有效的页码');
        }
    }

    // 初始化页面状态
    function initPageState() {
        const urlParams = new URLSearchParams(window.location.search);

        // 恢复筛选状态
        urlParams.forEach((value, key) => {
            if (key === 's') {
                // 恢复搜索关键词
                $('#cooperation-search').val(value);
            } else if (key === 'sort') {
                // 恢复排序选项
                $('#cooperation-sort').val(value);
            } else if (key === 'paged') {
                // 恢复页码
                currentPage = parseInt(value) || 1;
            } else {
                // 恢复筛选按钮状态
                const $filterBtn = $('[data-filter="' + key + '"][data-value="' + value + '"]');
                if ($filterBtn.length) {
                    // 移除同组其他按钮的激活状态
                    $filterBtn.siblings('.filter-btn:not(.more-btn)').removeClass('active');

                    // 激活当前按钮
                    $filterBtn.addClass('active');
                }
            }
        });
    }

    // 显示加载状态
    function showLoading() {
        isLoading = true;

        const $cooperationGrid = $('.cooperation-grid');
        $cooperationGrid.css({
            opacity: '0.6',
            pointerEvents: 'none'
        });

        // 添加加载指示器
        if (!$('#loading-indicator').length) {
            const $loadingIndicator = $(`
                <div id="loading-indicator" class="loading-overlay">
                    <div class="loading-spinner">
                        <div class="ant-spin ant-spin-spinning">
                            <span class="ant-spin-dot ant-spin-dot-spin">
                                <i class="ant-spin-dot-item"></i>
                                <i class="ant-spin-dot-item"></i>
                                <i class="ant-spin-dot-item"></i>
                                <i class="ant-spin-dot-item"></i>
                            </span>
                        </div>
                        <div class="loading-text">加载中...</div>
                    </div>
                </div>
            `);

            $('.cooperation-list').append($loadingIndicator);
        }
    }

    // 隐藏加载状态
    function hideLoading() {
        isLoading = false;

        const $cooperationGrid = $('.cooperation-grid');
        $cooperationGrid.css({
            opacity: '1',
            pointerEvents: 'auto'
        });

        $('#loading-indicator').remove();
    }

    // 初始化卡片动画
    function initCardAnimations() {
        $('.cooperation-card').each(function(index) {
            const $card = $(this);
            $card.css({
                opacity: '0',
                transform: 'translateY(20px)'
            });

            setTimeout(() => {
                $card.css({
                    transition: 'all 0.3s ease-out',
                    opacity: '1',
                    transform: 'translateY(0)'
                });
            }, index * 50);
        });
    }

    // 绑定卡片事件
    function bindCardEvents() {
        // 收藏功能
        $('.action-btn[title="收藏"]').off('click').on('click', function(e) {
            e.preventDefault();

            const $btn = $(this);
            if ($btn.hasClass('favorited')) {
                $btn.removeClass('favorited').css('color', '');
            } else {
                $btn.addClass('favorited').css('color', '#ff4d4f');
            }
        });

        // 分享功能
        $('.action-btn[title="分享"]').off('click').on('click', function(e) {
            e.preventDefault();

            const $card = $(this).closest('.cooperation-card');
            const title = $card.find('.cooperation-name a').text();
            const url = $card.find('.cooperation-name a').attr('href');

            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                });
            } else {
                // 复制链接到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url).then(() => {
                        showSuccess('链接已复制到剪贴板');
                    });
                }
            }
        });
    }

    // 全局函数
    window.clearFilters = function() {
        $('.filter-btn').removeClass('active');
        $('.filter-btn[data-value=""]').addClass('active');
        $('#cooperation-search').val('');
        $('#cooperation-sort').val('date_desc');
        currentPage = 1;
        applyFilters();
    };

    window.searchCooperation = function() {
        currentPage = 1;
        applyFilters();
    };

    window.sortCooperation = function() {
        currentPage = 1;
        applyFilters();
    };

    window.jumpToPage = jumpToPage;

    // 清除筛选条件
    window.clearFilters = function() {
        $('.filter-btn').removeClass('active');
        $('.filter-btn[data-value=""]').addClass('active');
        $('#cooperation-search').val('');
        $('#cooperation-sort').val('date_desc');
        applyFilters();
    };

    // 搜索功能
    window.searchCooperation = function() {
        applyFilters();
    };

    // 排序功能
    window.sortCooperation = function() {
        applyFilters();
    };

    // 文档就绪时初始化
    $(document).ready(function() {
        initCooperationFilters();
        initFavoriteFunction();
        initShareFunction();
        initCardAnimations();
    });

})(jQuery);
