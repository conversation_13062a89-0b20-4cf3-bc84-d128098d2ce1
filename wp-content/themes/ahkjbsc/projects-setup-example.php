<?php
/**
 * 项目自定义文章类型和字段配置示例
 * 这个文件展示了如何为projects自定义文章类型添加字段和分类法
 * 
 * @package ahkjbsc
 */

// 注册项目自定义文章类型
function register_projects_post_type() {
    $labels = array(
        'name'                  => '项目',
        'singular_name'         => '项目',
        'menu_name'             => '项目管理',
        'name_admin_bar'        => '项目',
        'archives'              => '项目档案',
        'attributes'            => '项目属性',
        'parent_item_colon'     => '父级项目:',
        'all_items'             => '所有项目',
        'add_new_item'          => '添加新项目',
        'add_new'               => '添加新项目',
        'new_item'              => '新项目',
        'edit_item'             => '编辑项目',
        'update_item'           => '更新项目',
        'view_item'             => '查看项目',
        'view_items'            => '查看项目',
        'search_items'          => '搜索项目',
        'not_found'             => '未找到项目',
        'not_found_in_trash'    => '回收站中未找到项目',
        'featured_image'        => '项目图片',
        'set_featured_image'    => '设置项目图片',
        'remove_featured_image' => '移除项目图片',
        'use_featured_image'    => '使用作为项目图片',
        'insert_into_item'      => '插入到项目',
        'uploaded_to_this_item' => '上传到此项目',
        'items_list'            => '项目列表',
        'items_list_navigation' => '项目列表导航',
        'filter_items_list'     => '筛选项目列表',
    );

    $args = array(
        'label'                 => '项目',
        'description'           => '项目信息管理',
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies'            => array('project_category'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 21,
        'menu_icon'             => 'dashicons-portfolio',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rewrite'               => array('slug' => 'projects'),
    );

    register_post_type('projects', $args);
}
add_action('init', 'register_projects_post_type');

// 注册项目分类法
function register_project_category_taxonomy() {
    $labels = array(
        'name'              => '项目分类',
        'singular_name'     => '项目分类',
        'search_items'      => '搜索项目分类',
        'all_items'        => '所有项目分类',
        'parent_item'       => '父级项目分类',
        'parent_item_colon' => '父级项目分类:',
        'edit_item'         => '编辑项目分类',
        'update_item'       => '更新项目分类',
        'add_new_item'      => '添加新项目分类',
        'new_item_name'     => '新项目分类名称',
        'menu_name'         => '项目分类',
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'project-category'),
        'public'            => true,
        'show_in_rest'      => true,
    );

    register_taxonomy('project_category', array('projects'), $args);
}
add_action('init', 'register_project_category_taxonomy');

// 如果使用ACF插件，可以通过以下方式注册字段组：
/*
if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
    'key' => 'group_projects_fields',
    'title' => '项目信息',
    'fields' => array(
        array(
            'key' => 'field_project_status',
            'label' => '项目状态',
            'name' => 'project_status',
            'type' => 'select',
            'instructions' => '请选择项目当前状态',
            'required' => 0,
            'choices' => array(
                '进行中' => '进行中',
                '已完成' => '已完成',
                '暂停' => '暂停',
                '已取消' => '已取消',
            ),
            'default_value' => '进行中',
        ),
        array(
            'key' => 'field_start_date',
            'label' => '启动时间',
            'name' => 'start_date',
            'type' => 'date_picker',
            'instructions' => '请选择项目启动时间',
            'required' => 0,
            'display_format' => 'Y-m-d',
            'return_format' => 'Y-m-d',
        ),
        array(
            'key' => 'field_investment_amount',
            'label' => '投资金额',
            'name' => 'investment_amount',
            'type' => 'text',
            'instructions' => '请输入投资金额（如：100万元）',
            'required' => 0,
        ),
        array(
            'key' => 'field_is_featured',
            'label' => '特色项目',
            'name' => 'is_featured',
            'type' => 'true_false',
            'instructions' => '是否为特色项目',
            'required' => 0,
            'default_value' => 0,
        ),
        array(
            'key' => 'field_project_leader',
            'label' => '项目负责人',
            'name' => 'project_leader',
            'type' => 'text',
            'instructions' => '请输入项目负责人姓名',
            'required' => 0,
        ),
        array(
            'key' => 'field_contact_phone',
            'label' => '联系电话',
            'name' => 'contact_phone',
            'type' => 'text',
            'instructions' => '请输入联系电话',
            'required' => 0,
        ),
        array(
            'key' => 'field_project_address',
            'label' => '项目地址',
            'name' => 'project_address',
            'type' => 'textarea',
            'instructions' => '请输入项目地址',
            'required' => 0,
            'rows' => 3,
        ),
        array(
            'key' => 'field_project_description',
            'label' => '项目简介',
            'name' => 'project_description',
            'type' => 'textarea',
            'instructions' => '请输入项目简介',
            'required' => 0,
            'rows' => 4,
        ),
        array(
            'key' => 'field_expected_completion',
            'label' => '预计完成时间',
            'name' => 'expected_completion',
            'type' => 'date_picker',
            'instructions' => '请选择预计完成时间',
            'required' => 0,
            'display_format' => 'Y-m-d',
            'return_format' => 'Y-m-d',
        ),
    ),
    'location' => array(
        array(
            array(
                'param' => 'post_type',
                'operator' => '==',
                'value' => 'projects',
            ),
        ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
));

endif;
*/

// 如果使用原生WordPress自定义字段，可以使用以下代码：

// 添加自定义字段元框
function add_projects_meta_boxes() {
    add_meta_box(
        'projects_details',
        '项目详细信息',
        'projects_meta_box_callback',
        'projects',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_projects_meta_boxes');

// 元框回调函数
function projects_meta_box_callback($post) {
    // 添加nonce字段
    wp_nonce_field('projects_meta_box', 'projects_meta_box_nonce');
    
    // 获取现有值
    $project_status = get_post_meta($post->ID, 'project_status', true);
    $start_date = get_post_meta($post->ID, 'start_date', true);
    $investment_amount = get_post_meta($post->ID, 'investment_amount', true);
    $is_featured = get_post_meta($post->ID, 'is_featured', true);
    $project_leader = get_post_meta($post->ID, 'project_leader', true);
    $contact_phone = get_post_meta($post->ID, 'contact_phone', true);
    $project_address = get_post_meta($post->ID, 'project_address', true);
    $project_description = get_post_meta($post->ID, 'project_description', true);
    $expected_completion = get_post_meta($post->ID, 'expected_completion', true);
    
    ?>
    <table class="form-table">
        <tr>
            <th><label for="project_status">项目状态</label></th>
            <td>
                <select id="project_status" name="project_status">
                    <option value="进行中" <?php selected($project_status, '进行中'); ?>>进行中</option>
                    <option value="已完成" <?php selected($project_status, '已完成'); ?>>已完成</option>
                    <option value="暂停" <?php selected($project_status, '暂停'); ?>>暂停</option>
                    <option value="已取消" <?php selected($project_status, '已取消'); ?>>已取消</option>
                </select>
            </td>
        </tr>
        <tr>
            <th><label for="start_date">启动时间</label></th>
            <td><input type="date" id="start_date" name="start_date" value="<?php echo esc_attr($start_date); ?>" /></td>
        </tr>
        <tr>
            <th><label for="investment_amount">投资金额</label></th>
            <td><input type="text" id="investment_amount" name="investment_amount" value="<?php echo esc_attr($investment_amount); ?>" placeholder="如：100万元" /></td>
        </tr>
        <tr>
            <th><label for="is_featured">特色项目</label></th>
            <td><input type="checkbox" id="is_featured" name="is_featured" value="1" <?php checked($is_featured, '1'); ?> /> 是否为特色项目</td>
        </tr>
        <tr>
            <th><label for="project_leader">项目负责人</label></th>
            <td><input type="text" id="project_leader" name="project_leader" value="<?php echo esc_attr($project_leader); ?>" /></td>
        </tr>
        <tr>
            <th><label for="contact_phone">联系电话</label></th>
            <td><input type="text" id="contact_phone" name="contact_phone" value="<?php echo esc_attr($contact_phone); ?>" /></td>
        </tr>
        <tr>
            <th><label for="project_address">项目地址</label></th>
            <td><textarea id="project_address" name="project_address" rows="3" cols="50"><?php echo esc_textarea($project_address); ?></textarea></td>
        </tr>
        <tr>
            <th><label for="project_description">项目简介</label></th>
            <td><textarea id="project_description" name="project_description" rows="4" cols="50"><?php echo esc_textarea($project_description); ?></textarea></td>
        </tr>
        <tr>
            <th><label for="expected_completion">预计完成时间</label></th>
            <td><input type="date" id="expected_completion" name="expected_completion" value="<?php echo esc_attr($expected_completion); ?>" /></td>
        </tr>
    </table>
    <?php
}

// 保存自定义字段
function save_projects_meta_box_data($post_id) {
    // 验证nonce
    if (!isset($_POST['projects_meta_box_nonce'])) {
        return;
    }
    
    if (!wp_verify_nonce($_POST['projects_meta_box_nonce'], 'projects_meta_box')) {
        return;
    }
    
    // 检查用户权限
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // 保存字段
    $fields = array(
        'project_status',
        'start_date', 
        'investment_amount',
        'project_leader',
        'contact_phone',
        'project_address',
        'project_description',
        'expected_completion'
    );
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
    
    // 特殊处理复选框
    $is_featured = isset($_POST['is_featured']) ? '1' : '0';
    update_post_meta($post_id, 'is_featured', $is_featured);
}
add_action('save_post', 'save_projects_meta_box_data');

// 添加一些示例项目分类
function add_sample_project_categories() {
    // 检查是否已经添加过示例数据
    if (get_option('sample_project_categories_added')) {
        return;
    }
    
    $categories = array(
        '人工智能' => '人工智能相关项目',
        '生物医药' => '生物医药相关项目',
        '新材料' => '新材料研发项目',
        '智能制造' => '智能制造相关项目',
        '新能源' => '新能源技术项目',
        '环保科技' => '环保科技项目',
        '信息技术' => '信息技术项目',
        '其他' => '其他类型项目'
    );
    
    foreach ($categories as $name => $description) {
        if (!term_exists($name, 'project_category')) {
            wp_insert_term($name, 'project_category', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
        }
    }
    
    // 标记已添加示例数据
    update_option('sample_project_categories_added', true);
}
add_action('init', 'add_sample_project_categories');

?>
