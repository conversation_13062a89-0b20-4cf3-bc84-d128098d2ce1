/*! This file is auto-generated */
(()=>{"use strict";var e={7734:e=>{e.exports=function e(r,t){if(r===t)return!0;if(r&&t&&"object"==typeof r&&"object"==typeof t){if(r.constructor!==t.constructor)return!1;var n,o,s;if(Array.isArray(r)){if((n=r.length)!=t.length)return!1;for(o=n;0!=o--;)if(!e(r[o],t[o]))return!1;return!0}if(r instanceof Map&&t instanceof Map){if(r.size!==t.size)return!1;for(o of r.entries())if(!t.has(o[0]))return!1;for(o of r.entries())if(!e(o[1],t.get(o[0])))return!1;return!0}if(r instanceof Set&&t instanceof Set){if(r.size!==t.size)return!1;for(o of r.entries())if(!t.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(r)&&ArrayBuffer.isView(t)){if((n=r.length)!=t.length)return!1;for(o=n;0!=o--;)if(r[o]!==t[o])return!1;return!0}if(r.constructor===RegExp)return r.source===t.source&&r.flags===t.flags;if(r.valueOf!==Object.prototype.valueOf)return r.valueOf()===t.valueOf();if(r.toString!==Object.prototype.toString)return r.toString()===t.toString();if((n=(s=Object.keys(r)).length)!==Object.keys(t).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(t,s[o]))return!1;for(o=n;0!=o--;){var i=s[o];if(!e(r[i],t[i]))return!1}return!0}return r!=r&&t!=t}}},r={};function t(n){var o=r[n];if(void 0!==o)return o.exports;var s=r[n]={exports:{}};return e[n](s,s.exports,t),s.exports}t.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},t.d=(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r);var n={};t.d(n,{default:()=>j});const o=window.wp.element,s=window.wp.data;var i=t(7734),u=t.n(i);const c=window.wp.compose,l=window.wp.i18n,a=window.wp.apiFetch;var f=t.n(a);const d=window.wp.url,p=window.wp.components,w=window.wp.blocks,h=window.ReactJSXRuntime,y={};function g({className:e}){return(0,h.jsx)(p.Placeholder,{className:e,children:(0,l.__)("Block rendered as empty.")})}function m({response:e,className:r}){const t=(0,l.sprintf)((0,l.__)("Error loading block: %s"),e.errorMsg);return(0,h.jsx)(p.Placeholder,{className:r,children:t})}function v({children:e,showLoader:r}){return(0,h.jsxs)("div",{style:{position:"relative"},children:[r&&(0,h.jsx)("div",{style:{position:"absolute",top:"50%",left:"50%",marginTop:"-9px",marginLeft:"-9px"},children:(0,h.jsx)(p.Spinner,{})}),(0,h.jsx)("div",{style:{opacity:r?"0.3":1},children:e})]})}function b(e){const{attributes:r,block:t,className:n,httpMethod:s="GET",urlQueryArgs:i,skipBlockSupportAttributes:l=!1,EmptyResponsePlaceholder:a=g,ErrorResponsePlaceholder:p=m,LoadingResponsePlaceholder:b=v}=e,x=(0,o.useRef)(!1),[j,S]=(0,o.useState)(!1),O=(0,o.useRef)(),[P,k]=(0,o.useState)(null),R=(0,c.usePrevious)(e),[A,M]=(0,o.useState)(!1);function T(){var e,n;if(!x.current)return;M(!0);const o=setTimeout((()=>{S(!0)}),1e3);let u=r&&(0,w.__experimentalSanitizeBlockAttributes)(t,r);l&&(u=function(e){const{backgroundColor:r,borderColor:t,fontFamily:n,fontSize:o,gradient:s,textColor:i,className:u,...c}=e,{border:l,color:a,elements:f,spacing:d,typography:p,...w}=e?.style||y;return{...c,style:w}}(u));const c="POST"===s,a=c?null:null!==(e=u)&&void 0!==e?e:null,p=function(e,r=null,t={}){return(0,d.addQueryArgs)(`/wp/v2/block-renderer/${e}`,{context:"edit",...null!==r?{attributes:r}:{},...t})}(t,a,i),h=c?{attributes:null!==(n=u)&&void 0!==n?n:null}:null,g=O.current=f()({path:p,data:h,method:c?"POST":"GET"}).then((e=>{x.current&&g===O.current&&e&&k(e.rendered)})).catch((e=>{x.current&&g===O.current&&k({error:!0,errorMsg:e.message})})).finally((()=>{x.current&&g===O.current&&(M(!1),S(!1),clearTimeout(o))}));return g}const _=(0,c.useDebounce)(T,500);(0,o.useEffect)((()=>(x.current=!0,()=>{x.current=!1})),[]),(0,o.useEffect)((()=>{void 0===R?T():u()(R,e)||_()}));const E=!!P,N=""===P,z=P?.error;return A?(0,h.jsx)(b,{...e,showLoader:j,children:E&&(0,h.jsx)(o.RawHTML,{className:n,children:P})}):N||!E?(0,h.jsx)(a,{...e}):z?(0,h.jsx)(p,{response:P,...e}):(0,h.jsx)(o.RawHTML,{className:n,children:P})}const x={},j=(0,s.withSelect)((e=>{const r=e("core/editor");if(r){const e=r.getCurrentPostId();if(e&&"number"==typeof e)return{currentPostId:e}}return x}))((({urlQueryArgs:e=x,currentPostId:r,...t})=>{const n=(0,o.useMemo)((()=>r?{post_id:r,...e}:e),[r,e]);return(0,h.jsx)(b,{urlQueryArgs:n,...t})}));(window.wp=window.wp||{}).serverSideRender=n.default})();